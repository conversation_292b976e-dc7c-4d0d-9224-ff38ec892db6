export function mergeObjects<T>(base: T, update: Partial<T>): T {
    const result = { ...base };

    for (const key in update) {
        const value = update[key];
        if (value !== null && value !== undefined) {
            result[key] = value;
        }
    }

    return result;
}

export function mergeObjectsDeep<T>(base: T, update: Partial<T>): T {
    if (typeof base !== "object" || base === null) return base;
    if (typeof update !== "object" || update === null) return base;

    const result = Array.isArray(base) ? [...base] : { ...base };

    for (const key in update) {
        const value = update[key];

        if (value === null || value === undefined) {
        continue;
        }


        if (
            typeof value === "object" &&
            !Array.isArray(value) &&
            typeof (base as any)[key] === "object" &&
            (base as any)[key] !== null
        ) {
            (result as any)[key] = mergeObjectsDeep(
                (base as any)[key],
                value as any
            );
        } else {
            (result as any)[key] = value;
        }
    }

    return result as T;
}