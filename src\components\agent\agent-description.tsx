import { Agent, AgentStatus, AgentType } from "@/models/agent"
import { Descriptions, DescriptionsProps, Skeleton, Tag } from "antd";
import {
    SyncOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";

export interface AgentDescriptionParams {
    children?: React.JSX.Element;
    locale?: string,
    agent?: Agent,
    layout?: 'vertical' | 'horizontal'
    
}

export function GetAgentModelFromType(agentType: AgentType){
    switch(agentType){
        case AgentType.Claude_v2:
            return "Anthropic Claude 2.0"
        case AgentType.Claude_v3_Haiku:
            return "Anthropic Claude 3 Haiku"
        case AgentType.Claude_v3_Sonnet:
            return "Anthropic Claude 3 Sonnet"
        case AgentType.Claude_v3_Opus:
            return "Anthropic Claude 3 Opus"
        case AgentType.GPT_4:
            return "OpenAI GPT-4"
        case AgentType.GPT_4_Turbo:
            return "OpenAI GPT-4 Turbo"
        case AgentType.GPT_3_5_Turbo:
            return "OpenAI GPT-3.5 Turbo"
        default:
            return "Unknown"
    }
}

export function GetAgentStatusTag(status: AgentStatus) {
    switch (status) {
        case AgentStatus.CREATING:
        case AgentStatus.DB_ENTRY_CREATED:
        case AgentStatus.PREPARING:
            return (<Tag icon={<SyncOutlined spin />} color="processing">creating</Tag>)
        case AgentStatus.QUEUED:
            return (<Tag icon={<ClockCircleOutlined />} color="default">waiting</Tag>)
        case AgentStatus.READY:
            return (<Tag icon={<CheckCircleOutlined />} color="success">ready</Tag>)
        case AgentStatus.SEALING:
        case AgentStatus.TAGGING:
        case AgentStatus.UPDATING:
        case AgentStatus.VERSIONING:
        case AgentStatus.WAITING_VERSION:
            return (<Tag icon={<SyncOutlined spin />} color="purple">deploying</Tag>)
        default:
            return (<Tag color="default">unknown</Tag>)

    }
}



const AgentDescription: React.FC<AgentDescriptionParams> = (params) => {
    const { TimeAgo } = useTimeAgo();

    const AgentDescriptionFields: DescriptionsProps['items'] = (params.agent) ? [
        {
            key: 'Name',
            label: 'Name',
            children: params.agent.name,
        },
        {
            key: 'Description',
            label: 'Description',
            children: params.agent.description,
            span: 2,
        },
        {
            key: 'Instructions',
            label: 'Instructions',
            children: params.agent.instructions,
            span: 3,
        },
        {
            key: 'Status',
            label: 'Status',
            dataIndex: 'status',
            children: GetAgentStatusTag(params.agent.status),
        },
        {
            label: 'Agent Model',
            key: 'Agent Model',
            dataIndex: 'agentType',
            children: GetAgentModelFromType(params.agent.agentType),
        },
        {
            key: 'Last Modified',
            label: 'Last Modified',
            children: (<TimeAgo date={params.agent.lastChangeTimestamp * 1000} locale="en-US" live={true} />),
        },
        {
            key: 'Created At',
            label: 'Created At',
            children: (new Date(params.agent.createdAt * 1000)).toLocaleDateString(params.locale),
        },
    ].filter(item => item.children !== undefined) : [];



    return ((params.agent) ? <Descriptions items={AgentDescriptionFields} layout={params.layout ?? 'horizontal'}></Descriptions> : <Skeleton />)
}

export default AgentDescription;