import { Button, Dropdown, MenuProps, Space, TableColumnsType, Tag } from "antd";
import {
    DeleteOutlined,
    EllipsisOutlined,
    EditOutlined,
    CopyOutlined,
    GroupOutlined
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";
import DataTable from "@/components/data-table";
import React from "react";
import { ToolGroup, ToolGroupActions, ToolGroupController } from "@/models/toolgroup";

interface ToolGroupListParams {
    onClick: (record: ToolGroup) => void;
    onDelete: (record: ToolGroup) => void;
    onEdit: (record: ToolGroup) => void;
    onDuplicate: (record: ToolGroup) => void;
    mutateObjects?: ToolGroup[];
}

const ToolGroupList: React.FC<ToolGroupListParams> = (params: ToolGroupListParams) => {
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState(false);
    const { TimeAgo } = useTimeAgo();

    const contextMenuProps = (record: ToolGroup): MenuProps => {
        const controller = new ToolGroupController();
        return {
            items: [
                {
                    key: 'edit',
                    label: 'Edit',
                    icon: <EditOutlined />,
                    disabled: !controller.can(ToolGroupActions.EDIT, record),
                    onClick: () => params.onEdit(record)
                },
                {
                    key: 'duplicate',
                    label: 'Duplicate',
                    icon: <CopyOutlined />,
                    disabled: !controller.can(ToolGroupActions.DUPLICATE, record),
                    onClick: () => params.onDuplicate(record)
                },
                {
                    type: 'divider'
                },
                {
                    key: 'delete',
                    label: 'Delete',
                    icon: <DeleteOutlined />,
                    disabled: !controller.can(ToolGroupActions.DELETE, record),
                    onClick: () => params.onDelete(record),
                    danger: true
                }
            ]
        };
    };

    const columns: TableColumnsType<ToolGroup> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Name', dataIndex: 'name', render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>
                    <Space>
                        <GroupOutlined />
                        {value}
                    </Space>
                </a>;
            }
        },
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'Tools Count', dataIndex: 'toolIds', render: (toolIds: string[]) => {
                return <Tag color="blue">
                    {toolIds ? toolIds.length : 0} tools
                </Tag>;
            }
        },
        {
            title: 'Last Modified', dataIndex: 'lastChangeTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<ToolGroup, ToolGroupController>
            controller={new ToolGroupController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: ToolGroup) => false}
            mutateItems={params.mutateObjects}
            rowKey="id"
        />
    )
}

export default ToolGroupList;
