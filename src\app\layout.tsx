import type { Metadata } from "next";
import React from 'react';
import "./globals.css";
import i18nConfig from '@/app/i18nConfig';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ConfigProvider, theme } from 'antd';
import NotificationComponent from '@/components/notification-bus'

export const metadata: Metadata = {
  title: "Coral Agents",
  description: "Deploy fully featured AI Agents in minutes.",
};

export function generateStaticParams() {
  return i18nConfig.locales.map((locale) => ({ locale }));
}

export default async function RootLayout({
  children, params
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>
}>) {
  return (
    <html lang={(await params).locale}>
      <body>
        <AntdRegistry>
          <ConfigProvider
            theme={{
              algorithm: theme.defaultAlgorithm,
              "token": {
                "colorPrimary": "#c62c61",
                "colorInfo": "#c62c61",
              },
            }}
          >
            <NotificationComponent />
            {children}
          </ConfigProvider>
        </AntdRegistry>
      </body>
    </html>
  );
}