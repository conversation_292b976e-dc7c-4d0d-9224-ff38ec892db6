import initTranslations from '@/app/i18n';
import TranslationProvider from '@/app/[locale]/TranslationProvider';

const i18nNamespaces = ['layout', 'login'];


export default async function Layout({ children, params }: any) {

  const locale : string = (await params).locale;
  const { resources } = await initTranslations(locale, i18nNamespaces);

  return (
    <TranslationProvider locale={locale} resources={resources} namespaces={i18nNamespaces}>
      {children}
    </TranslationProvider>
  );
}
