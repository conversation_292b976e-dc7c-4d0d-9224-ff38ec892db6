import { <PERSON><PERSON>, Di<PERSON><PERSON>, Form, FormInstance, Input, Radio, Select, Space } from "antd";
import { BaseNodeData, BaseNodeFormElement } from "../base-node";
import { SetAgentNodeModelData } from "./set-agent-model";
import React from "react";
import SubmitButton from "@/components/submit-button";
import SearchAndDescriptionItem from "@/components/formitems/search-and-description";
import { Agent, AgentController } from "@/models/agent";
import AgentDescription from "@/components/agent/agent-description";
import {RobotOutlined} from '@ant-design/icons';
import { DefaultOptionType } from "antd/es/select";
export interface SetAgentFormProps extends BaseNodeFormElement<SetAgentNodeModelData>{
}


function SetAgentForm(props: SetAgentFormProps) {

    const [label, setLabel] = React.useState('Agent Alias');
    const [isAlias, setIsAlias] = React.useState<boolean>();
    const updateLabel = (alias : boolean) => {
        setIsAlias(alias);
        setLabel(alias ? 'Agent Alias' : 'Agent Tag');
    }
    const [form] = Form.useForm<SetAgentNodeModelData>();
    const [selectedAgentId, setSelectedAgentId] = React.useState<string>();
    const agentController = new AgentController();

    const useListTags = React.useCallback(() => agentController.useListTags({ count: 1000 }, selectedAgentId), [selectedAgentId]);
    const {data : tagsData, isLoading : isLoadingTags, isValidating : isValidatingTags} = useListTags()

    const useListAliases = React.useCallback(() => agentController.useListAliases({ count: 1000 }, selectedAgentId), [selectedAgentId]);
    const {data : aliasesData, isLoading : isLoadingAliases, isValidating : isValidatingAliases} = useListAliases()

    const [tagOrAliasOptions, setTagOrAliasOptions] = React.useState<DefaultOptionType[]>([]);

    const submitForm = (form : FormInstance<SetAgentNodeModelData>) => {
        return { 
            agentId : form.getFieldValue("agentId"),
            isAlias: form.getFieldValue("isAlias"),
            agentAliasOrTag: form.getFieldValue("agentAliasOrTag")
        }
    } 

    React.useEffect(()=>{
        if(!isAlias && tagsData !== undefined){
            setTagOrAliasOptions(tagsData.entries.map(e => ({value: e.tag, label: e.tag})))
        }else if(isAlias && aliasesData !== undefined){
            setTagOrAliasOptions(aliasesData.entries.map(e => ({value: e.agentTag, label: e.agentTag})))
        }
    }, [tagsData, aliasesData, isAlias])

    return (
        <Form 
            form={form} 
            layout="vertical" 
            onFinish={() => {props.onChange(submitForm(form)); props.onCancel()}}
            initialValues={{agentType:'alias', ...props.data}}
            >
            <Form.Item label="Agent" name="agentId">
            <SearchAndDescriptionItem<Agent>
                    searchIcon={<RobotOutlined />}
                    searchResults={6}
                    controller={agentController}
                    placeholder="Agent"
                    getDescription={(item : Agent) => {
                        return (<AgentDescription agent={item} />)    
                    }}
                    onChange={(agentId)=>{
                        setSelectedAgentId(agentId)
                    }}
                />
            </Form.Item>
            <Form.Item label="Call Agent By" name="agentType">
                <Radio.Group 
                    onChange={(e) => {
                        updateLabel(e.target.value == 'alias');
                    }}
                    options={[
                        {value: 'alias', label: 'Alias'}, {value: 'tag', label: 'Tag'}
                    ]}
                    optionType="button"
                />
            </Form.Item>
            <Form.Item label={label} name="agentAliasOrTag">
                <Select className='nodrag' options={tagOrAliasOptions} loading={isLoadingTags || isLoadingAliases || isValidatingTags || isValidatingAliases} />
            </Form.Item>
            <Space direction="horizontal">
                <SubmitButton form={form}>Save Changes</SubmitButton>
                <Button onClick={() => props.onCancel()}>Cancel</Button>
            </Space>
        </Form>
    )
}

export default SetAgentForm;