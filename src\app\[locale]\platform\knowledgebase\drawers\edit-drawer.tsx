import { CreateKnowledgeBaseRequest, KnowledgeBase, KnowledgebaseController, putKnowledgeBase, UpdateKnowledgeBaseRequest } from "@/models/knowledgebase";
import DrawerTemplate, { DrawerFormTemplateProperties, DrawerTemplateErrorEmpty, makeDrawerFormFields } from "@/templates/drawer-template";
import { Button, Form, Input, Typography } from "antd";
import React, { useEffect } from "react";
import '@ant-design/v5-patch-for-react-19';
import { Fetcher } from "@/functions/fetcher";

interface KnowledgebaseEditDrawerParams {
    isOpen : boolean;
    knowledgebase? : KnowledgeBase;
    onSuccess : (data : KnowledgeBase) => void;
    mode : 'edit' | 'create';
}

const KnowledgebaseEditDrawer: React.FC<KnowledgebaseEditDrawerParams> = (params: KnowledgebaseEditDrawerParams) => {
    const knowledgebaseController : KnowledgebaseController = new KnowledgebaseController();

    const [form] = Form.useForm<KnowledgeBase>();
    const [clientReady, setClientReady] = React.useState<boolean>(false);
    const characterCounter = Form.useWatch('description', form);
    const [drawerError, setDrawerError] = React.useState(DrawerTemplateErrorEmpty)
    const { data, trigger, isMutating } = knowledgebaseController.useUpdate(params.knowledgebase?.kbId);
    const { data : dataCreate, trigger: triggerCreate, isMutating: isMutatingCreate } = knowledgebaseController.useCreate();
    const [fieldsData, setFieldsData] = React.useState<any[]>([]);

    useEffect(() => {
        setClientReady(true);
    }, [])

    useEffect(() => {
        if(data === undefined) return;
        setDrawerError(DrawerTemplateErrorEmpty);
        if (params.onSuccess != null) params.onSuccess(data ?? dataCreate);
    }, [data, dataCreate])

    useEffect(() => {
        if (params.isOpen) {
            form.resetFields();
            setDrawerError(DrawerTemplateErrorEmpty);
            setFieldsData(makeDrawerFormFields(params.knowledgebase));
        }
    }, [params.isOpen])

    const onFinish = (values: KnowledgeBase) => {
        let request = { name: values.name, description: values.description }
        if(params.mode === 'create'){
            triggerCreate({body: request}).catch((reason: Fetcher.FetcherError) => {
                setDrawerError({ message: reason.message, title: 'Something went wrong with your request.', show: true });
            });
        }else{
            trigger({body: request}).catch((reason: Fetcher.FetcherError) => {
                setDrawerError({ message: reason.message, title: 'Something went wrong with your request.', show: true });
            });
        }
    };

    return (
        <DrawerTemplate
            error={drawerError}
            isLoading={isMutating || isMutatingCreate}
            title={(params.mode === 'create') ? 'Create a Knowledge Base' : 'Editing ' + params.knowledgebase?.name}
        >
            <Form form={form} fields={fieldsData} layout='vertical' onFinish={onFinish} scrollToFirstError={{ behavior: 'instant', block: 'end', focus: true }}>
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[{ max: 64, message: 'Please input up to 64 characters.' }, { min: 3, message: 'Your knowledge base name must have at least 3 characters.' }, { required: true, message: 'Please input a name for the knowledge base' }]}
                >
                    <Input />
                </Form.Item>
                <Form.Item label="Description">
                    <Form.Item
                        name="description"
                        rules={[{ max: 250, message: 'Please input a description of up to 250 characters' }]}
                        noStyle
                    >
                        <Input.TextArea rows={4} />
                    </Form.Item>
                    <Typography.Text type={(characterCounter?.length || 0) > 250 ? 'danger' : 'secondary'}>{characterCounter?.length.toString() || '0'}/250 characters</Typography.Text>
                </Form.Item>
                <Form.Item shouldUpdate>
                    {() => (
                        <Button
                            type="primary"
                            htmlType="submit"
                            disabled={
                                !clientReady ||
                                ! (params.mode === 'create' ? form.isFieldTouched("name") : form.isFieldsTouched()) ||
                                !!form.getFieldsError().filter(({ errors }) => errors.length).length
                            }
                        >
                            {(params.mode === 'create') ? "Create" : "Save"}
                        </Button>
                    )}
                </Form.Item >
            </Form>
        </DrawerTemplate>
    )
}

export default KnowledgebaseEditDrawer;