import { IBasemodel } from "@/models/base-model";
import { Empty, Table } from "antd";
import React, { useEffect } from "react";
import type { TableColumnsType } from 'antd';
import { ItemMutation } from "./lazydatasource/lazymutation";
import { ItemUpdateStrategy, LazyDataSource } from "./lazydatasource/lazydatasource";


// DataTable2 parameters interface
export interface DataTable2Params<T extends IBasemodel> {
    dataSource: LazyDataSource<T>;
    itemUpdateInterval?: number;
    itemUpdateStrategy?: ItemUpdateStrategy;
    tableColumns: TableColumnsType<T>;
    noDataDetails?: (isError: boolean) => React.JSX.Element;
    invalidate?: boolean;
    onItemsValidatingChange?: (isValidating: boolean, validatingIdList: string[]) => void;
    onDataChange?: (entries: T[]) => void;
    shouldInvalidate?: (entry: T) => boolean;
    mutations?: ItemMutation<T>[];
    pageSize?: number;
    currentPage?: number;
    onPageChange?: (page: number, pageSize: number) => void;
}


// DataTable2 component with LazyDataSource integration
export function DataTable2<T extends IBasemodel>(params: DataTable2Params<T>) {
    const [currentPage, setCurrentPage] = React.useState<number>(params.currentPage || 1);
    const [pageSize, setPageSize] = React.useState<number>(params.pageSize || 10);

    // Get controller from LazyDataSourceList if available
    const controller = (params.dataSource as any).controller;

    // Configure data source
    React.useEffect(() => {
        if (params.itemUpdateInterval) {
            params.dataSource.setItemUpdateInterval(params.itemUpdateInterval);
        }
        if (params.itemUpdateStrategy) {
            params.dataSource.setItemUpdateStrategy(params.itemUpdateStrategy);
        }
        if (params.shouldInvalidate) {
            params.dataSource.setShouldInvalidate(params.shouldInvalidate);
            params.dataSource.startItemUpdateCycle();
        }

        // Set initial page size and current page
        params.dataSource.setPageSize(pageSize);
        params.dataSource.setCurrentPage(currentPage);

        return () => {
            params.dataSource.stopItemUpdateCycle();
        };
    }, [params.dataSource, params.itemUpdateInterval, params.itemUpdateStrategy, params.shouldInvalidate, pageSize, currentPage]);

    // Sync pageSize changes from props
    React.useEffect(() => {
        if (params.pageSize !== undefined && params.pageSize !== pageSize) {
            setPageSize(params.pageSize);
            params.dataSource.setPageSize(params.pageSize);
        }
    }, [params.pageSize, pageSize, params.dataSource]);

    // Sync currentPage changes from props
    React.useEffect(() => {
        if (params.currentPage !== undefined && params.currentPage !== currentPage) {
            setCurrentPage(params.currentPage);
            params.dataSource.setCurrentPage(params.currentPage);
        }
    }, [params.currentPage, currentPage, params.dataSource]);

    // Hook for updating items that need refresh
    const [itemsToUpdate, setItemsToUpdate] = React.useState<string[]>([]);
    const shouldFetchItems = itemsToUpdate.length > 0;
    const { data: statusData, error: statusError } = controller?.useGet(
        shouldFetchItems ? itemsToUpdate : [],
        {
            refreshInterval: shouldFetchItems ? (params.itemUpdateInterval || 10000) : 0,
            revalidateIfStale: shouldFetchItems
        }
    ) || { data: undefined, error: undefined };

    // Set up data source callbacks
    useEffect(() => {
        params.dataSource.onDataChange((data: T[]) => {
            if (params.onDataChange) {
                params.onDataChange(data);
            }
        });

        params.dataSource.onLoadingChange((_isLoading: boolean) => {
            // Loading state is handled through the data source
        });

        params.dataSource.onItemsValidatingChange((isValidating: boolean, validatingIds: string[]) => {
            if (isValidating && validatingIds.length > 0) {
                setItemsToUpdate(validatingIds);
            } else {
                setItemsToUpdate([]);
            }

            if (params.onItemsValidatingChange) {
                params.onItemsValidatingChange(isValidating, validatingIds);
            }
        });
    }, [params.dataSource, params.onDataChange, params.onItemsValidatingChange]);

    // Handle invalidation
    useEffect(() => {
        if (params.invalidate !== undefined) {
            params.dataSource.invalidate();
        }
    }, [params.invalidate, params.dataSource]);

    // Handle mutations
    useEffect(() => {
        if (!params.mutations || params.mutations.length === 0) return;
        params.dataSource.applyMutations(params.mutations);
    }, [params.mutations, params.dataSource]);

    // Handle status data updates from SWR
    useEffect(() => {
        if (!controller || itemsToUpdate.length === 0) return;

        // Process each item update
        itemsToUpdate.forEach(itemId => {
            const itemData = statusData ? statusData.filter((item: T) => controller.getId(item) === itemId) : [];
            params.dataSource.updateItemFromSWR(itemId, itemData, statusError);
            params.dataSource.markItemAsUpdating(itemId);
        });

        // Complete the update cycle if all items are processed
        params.dataSource.completeUpdateCycle();
    }, [statusData, statusError, itemsToUpdate, controller, params.dataSource]);

    // Handle pagination changes
    const handleTableChange = async (pagination: any) => {
        const newPage = pagination.current || 1;
        const newPageSize = pagination.pageSize || pageSize;

        setCurrentPage(newPage);
        setPageSize(newPageSize);

        // Update data source
        params.dataSource.setCurrentPage(newPage);
        params.dataSource.setPageSize(newPageSize);

        // Notify parent component if callback is provided
        if (params.onPageChange) {
            params.onPageChange(newPage, newPageSize);
        }
    };

    // Initial data load - only run once when component mounts
    useEffect(() => {
        if (params.dataSource.getData().length === 0 && !params.dataSource.isLoading()) {
            params.dataSource.loadMore();
        }
    }, []); // Empty dependency array to run only once

    const data = params.dataSource.getData();
    const totalCount = params.dataSource.getTotalCount();
    const isLoading = params.dataSource.isLoading();

    return (
        <Table<T>
            rowSelection={{ type: 'checkbox' }}
            loading={isLoading}
            columns={params.tableColumns}
            dataSource={data}
            rowKey={(record: T) => controller?.getId(record) || 'id'}
            pagination={{
                position: ['bottomCenter'],
                current: currentPage,
                pageSize: pageSize,
                total: totalCount,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                    `${range[0]}-${range[1]} of ${total} items (${params.dataSource.getLoadedCount()} loaded)`
            }}
            onChange={handleTableChange}
            locale={{
                emptyText: <Empty description="No Data">
                    {params.noDataDetails ? params.noDataDetails(false) : null}
                </Empty>
            }}
        />
    );
}

export default DataTable2;
