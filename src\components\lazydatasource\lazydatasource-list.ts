import { mergeObjectsDeep } from "@/functions/object-helper";
import { ItemUpdateStrategy, LazyDataSource } from "./lazydatasource";
import { Fetcher2 } from "@/functions/fetcher2";
import { ItemMutation, MutationOperation } from "./lazymutation";
import { IBasemodel, IBasemodelController, ListResponse } from "@/models/base-model";
import React from "react";

// LazyDataSourceList implementation using IBasemodelController
export class LazyDataSourceList<T extends IBasemodel> implements LazyDataSource<T> {
    private controller: IBasemodelController<T, any>;
    private data: T[] = [];
    private totalCount: number = 0;
    private nextToken?: string;
    private loading: boolean = false;
    private minItemsPerRequest: number = 50;
    private autoLoadUntilExhaustion: boolean = false;
    private dataChangeCallback?: (data: T[]) => void;
    private loadingChangeCallback?: (isLoading: boolean) => void;
    private itemsValidatingChangeCallback?: (isValidating: boolean, validatingIds: string[]) => void;
    private hasInitialLoad: boolean = false;
    private exhausted: boolean = false;
    private currentRequest?: { count: number; next?: string };
    private mutateFunction?: any;
    private hasBeenInvalidated: boolean = false;

    // Item update management
    private shouldInvalidatePredicate?: (item: T) => boolean;
    private itemUpdateInterval: number = 10000;
    private itemUpdateStrategy: ItemUpdateStrategy = ItemUpdateStrategy.REPLACE;
    private itemUpdateCycleActive: boolean = false;
    private itemsToUpdate: Set<string> = new Set();
    private updatingItems: boolean = false;
    private updateCycleTimeout?: NodeJS.Timeout;

    // Pagination management
    private pageSize: number = 10;
    private currentPage: number = 1;
    private pendingPageLoad?: { page: number; pageSize: number; resolve: () => void; reject: (error: any) => void };
    private loadingForPage: boolean = false;
    private requestCallback?: (request: { count: number; next?: string }) => void;

    constructor(controller: IBasemodelController<T, any>) {
        this.controller = controller;
    }

    // Method to be called from component with SWR result
    updateFromSWR(data: ListResponse<T> | undefined, isLoading: boolean, mutate: any): void {
        this.mutateFunction = mutate;

        if (isLoading !== this.loading) {
            this.loading = isLoading;
            this.loadingChangeCallback?.(isLoading);
        }

        if (data) {
            if (!this.hasInitialLoad) {
                // First load - set total count and reset data
                this.totalCount = data.total;
                this.hasInitialLoad = true;
                this.data = [...data.entries];
                // Clear any pending request since we got the initial data
                this.currentRequest = undefined;
            } else {
                // Subsequent loads - append data
                this.data.push(...data.entries);
                this.currentRequest = undefined;
            }

            this.nextToken = data.next;

            if (!this.nextToken) {
                this.exhausted = true;
                // Update total count to match actual loaded data if no more data
                this.totalCount = this.data.length;
            }

            this.notifyDataChange();

            // Check if we completed a pending page load
            if (this.pendingPageLoad && !isLoading) {
                const { page, pageSize, resolve } = this.pendingPageLoad;
                if (this.isPageFullyLoaded(page, pageSize)) {
                    this.pendingPageLoad = undefined;
                    this.loadingForPage = false;
                    resolve();
                }
            }

            // Auto-load if enabled and not exhausted
            if (this.autoLoadUntilExhaustion && !this.exhausted && !isLoading) {
                setTimeout(() => this.loadMore(), 0);
            }
        }
    }

    getCurrentRequest(): { count: number; next?: string } | undefined {
        return this.currentRequest;
    }

    getData(): T[] {
        return this.data;
    }

    getTotalCount(): number {
        return this.totalCount;
    }

    getLoadedCount(): number {
        return this.data.length;
    }

    isLoading(): boolean {
        return this.loading;
    }

    isUpdatingItems(): boolean {
        return this.updatingItems;
    }

    getMaxFullPage(): number {
        if (this.pageSize === 0) return 0;
        return Math.floor(this.data.length / this.pageSize);
    }

    isPageFullyLoaded(page: number, pageSize: number): boolean {
        const requiredItems = page * pageSize;
        return this.data.length >= requiredItems || this.exhausted;
    }

    private notifyDataChange(): void {
        this.dataChangeCallback?.(this.data);
    }

    async loadMore(): Promise<void> {
        if (this.loading || this.exhausted || this.currentRequest) return;

        // Only allow loading more if we haven't loaded initial data or if we've been explicitly invalidated
        if (this.hasInitialLoad && !this.hasBeenInvalidated && this.data.length > 0) {
            // We already have data and haven't been invalidated, so only load more if we need more data
            // This prevents unnecessary requests
        }

        this.currentRequest = {
            count: this.minItemsPerRequest,
            next: this.nextToken
        };

        // Reset the invalidation flag since we're making a new request
        this.hasBeenInvalidated = false;

        // Notify the hook about the new request via callback
        if (this.requestCallback && this.currentRequest) {
            this.requestCallback(this.currentRequest);
        }
    }

    async loadUntilPage(page: number, pageSize: number): Promise<void> {
        // If page is already fully loaded, return immediately
        if (this.isPageFullyLoaded(page, pageSize)) {
            return;
        }

        // If we're already loading for this page, wait for it
        if (this.pendingPageLoad && this.pendingPageLoad.page === page && this.pendingPageLoad.pageSize === pageSize) {
            return new Promise((resolve, reject) => {
                const originalResolve = this.pendingPageLoad!.resolve;
                const originalReject = this.pendingPageLoad!.reject;
                this.pendingPageLoad!.resolve = () => {
                    originalResolve();
                    resolve();
                };
                this.pendingPageLoad!.reject = (error) => {
                    originalReject(error);
                    reject(error);
                };
            });
        }

        // Set up pending page load
        return new Promise((resolve, reject) => {
            this.pendingPageLoad = { page, pageSize, resolve, reject };
            this.loadingForPage = true;
            this.triggerLoadForPage(page, pageSize);
        });
    }

    private async triggerLoadForPage(page: number, pageSize: number): Promise<void> {
        const requiredItems = page * pageSize;

        while (this.data.length < requiredItems && !this.exhausted && !this.loading) {
            await this.loadMore();

            // Wait for the loading state to change using the loading callback
            if (this.loading) {
                await new Promise<void>(resolve => {
                    const originalCallback = this.loadingChangeCallback;
                    this.loadingChangeCallback = (isLoading: boolean) => {
                        if (originalCallback) originalCallback(isLoading);
                        if (!isLoading) {
                            this.loadingChangeCallback = originalCallback;
                            resolve();
                        }
                    };
                });
            }
        }

        // If we've loaded enough or exhausted, resolve the pending load
        if (this.pendingPageLoad && (this.isPageFullyLoaded(page, pageSize) || this.exhausted)) {
            const { resolve } = this.pendingPageLoad;
            this.pendingPageLoad = undefined;
            this.loadingForPage = false;
            resolve();
        }
    }

    async loadAll(): Promise<void> {
        this.setAutoLoadUntilExhaustion(true);
    }

    invalidate(): void {
        this.data = [];
        this.totalCount = 0;
        this.nextToken = undefined;
        this.hasInitialLoad = false;
        this.exhausted = false;
        this.currentRequest = undefined;
        this.hasBeenInvalidated = true;
        this.notifyDataChange();

        // Trigger SWR revalidation if available
        if (this.mutateFunction) {
            this.mutateFunction();
        }
    }

    replaceItem(item: T): void {
        const id = this.controller.getId(item);
        const index = this.data.findIndex(existing => this.controller.getId(existing) === id);
        
        if (index >= 0) {
            this.data[index] = item;
        } else {
            this.data.push(item);
        }
        this.notifyDataChange();
    }

    mergeItem(item: Partial<T>): void {
        // Find item by trying to get ID from the partial item
        let id: string;
        try {
            id = this.controller.getId(item as T);
        } catch {
            console.error('Cannot merge item without valid ID');
            return;
        }

        const index = this.data.findIndex(existing => this.controller.getId(existing) === id);
        
        if (index >= 0) {
            this.data[index] = mergeObjectsDeep(this.data[index], item);
            this.notifyDataChange();
        }
    }

    deleteItem(id: string): void {
        const index = this.data.findIndex(item => this.controller.getId(item) === id);
        if (index >= 0) {
            this.data.splice(index, 1);
            this.totalCount = Math.max(0, this.totalCount - 1);
            this.notifyDataChange();
        }
    }

    applyMutations(mutations: ItemMutation<T>[]): void {
        mutations.forEach(mutation => {
            switch (mutation.operation) {
                case MutationOperation.REPLACE:
                    if (mutation.item) {
                        this.replaceItem(mutation.item);
                    }
                    break;
                case MutationOperation.MERGE:
                    if (mutation.partialItem) {
                        this.mergeItem(mutation.partialItem);
                    }
                    break;
                case MutationOperation.DELETE:
                    if (mutation.id) {
                        this.deleteItem(mutation.id);
                    }
                    break;
            }
        });
    }

    setMinItemsPerRequest(count: number): void {
        this.minItemsPerRequest = count;
    }

    setAutoLoadUntilExhaustion(enabled: boolean): void {
        this.autoLoadUntilExhaustion = enabled;
        if (enabled && !this.exhausted && !this.loading && !this.currentRequest) {
            this.loadMore();
        }
    }

    setPageSize(pageSize: number): void {
        if (this.pageSize !== pageSize) {
            this.pageSize = pageSize;
            // Check if we need to load more data for the current page
            this.checkAndLoadForCurrentPage();
        }
    }

    getPageSize(): number {
        return this.pageSize;
    }

    getCurrentPage(): number {
        return this.currentPage;
    }

    setCurrentPage(page: number): void {
        if (this.currentPage !== page) {
            this.currentPage = page;
            // Check if we need to load more data for the new page
            this.checkAndLoadForCurrentPage();
        }
    }

    private checkAndLoadForCurrentPage(): void {
        if (!this.isPageFullyLoaded(this.currentPage, this.pageSize) && !this.exhausted && !this.loadingForPage) {
            this.loadUntilPage(this.currentPage, this.pageSize).catch(error => {
                console.error('Failed to load page:', error);
            });
        }
    }

    onDataChange(callback: (data: T[]) => void): void {
        this.dataChangeCallback = callback;
    }

    onLoadingChange(callback: (isLoading: boolean) => void): void {
        this.loadingChangeCallback = callback;
    }

    onItemsValidatingChange(callback: (isValidating: boolean, validatingIds: string[]) => void): void {
        this.itemsValidatingChangeCallback = callback;
    }

    // Item update management methods
    setShouldInvalidate(predicate: (item: T) => boolean): void {
        this.shouldInvalidatePredicate = predicate;
        this.updateItemsToUpdate();
    }

    setItemUpdateInterval(interval: number): void {
        this.itemUpdateInterval = interval;
    }

    setItemUpdateStrategy(strategy: ItemUpdateStrategy): void {
        this.itemUpdateStrategy = strategy;
    }

    startItemUpdateCycle(): void {
        if (this.itemUpdateCycleActive) return;
        this.itemUpdateCycleActive = true;
        this.scheduleNextUpdateCycle();
    }

    stopItemUpdateCycle(): void {
        this.itemUpdateCycleActive = false;
        if (this.updateCycleTimeout) {
            clearTimeout(this.updateCycleTimeout);
            this.updateCycleTimeout = undefined;
        }
    }

    private updateItemsToUpdate(): void {
        if (!this.shouldInvalidatePredicate) return;

        const newItemsToUpdate = new Set<string>();
        this.data.forEach(item => {
            if (this.shouldInvalidatePredicate!(item)) {
                newItemsToUpdate.add(this.controller.getId(item));
            }
        });

        this.itemsToUpdate = newItemsToUpdate;
    }

    private scheduleNextUpdateCycle(): void {
        if (!this.itemUpdateCycleActive) return;

        this.updateItemsToUpdate();

        if (this.itemsToUpdate.size === 0) {
            // No items to update, schedule next cycle
            this.updateCycleTimeout = setTimeout(() => {
                this.scheduleNextUpdateCycle();
            }, this.itemUpdateInterval);
            return;
        }

        // Start the update cycle
        this.updatingItems = true;
        this.itemsValidatingChangeCallback?.(true, Array.from(this.itemsToUpdate));

        // The actual item updates will be handled by the component via SWR
        // We just need to wait for the interval before the next cycle
        this.updateCycleTimeout = setTimeout(() => {
            this.scheduleNextUpdateCycle();
        }, this.itemUpdateInterval);
    }

    // This method will be called by the component with SWR results
    updateItemFromSWR(itemId: string, data: T[] | undefined, error: any): void {
        if (data && data.length > 0) {
            const updatedItem = data[0];
            this.handleItemUpdate(updatedItem);
        } else if (error) {
            this.handleItemUpdateError(itemId, error);
        }
    }

    // Get the current items that need updating
    getItemsToUpdate(): string[] {
        return Array.from(this.itemsToUpdate);
    }

    // Mark an item as being processed for update
    markItemAsUpdating(itemId: string): void {
        // Remove from items to update since it's being processed
        this.itemsToUpdate.delete(itemId);
    }

    // Complete the current update cycle
    completeUpdateCycle(): void {
        this.updatingItems = false;
        this.itemsValidatingChangeCallback?.(false, []);
    }

    private handleItemUpdate(updatedItem: T): void {
        const itemId = this.controller.getId(updatedItem);
        const index = this.data.findIndex(item => this.controller.getId(item) === itemId);

        if (index >= 0) {
            if (this.itemUpdateStrategy === ItemUpdateStrategy.MERGE) {
                this.data[index] = mergeObjectsDeep(this.data[index], updatedItem);
            } else {
                this.data[index] = updatedItem;
            }
            this.notifyDataChange();
        }
    }

    private handleItemUpdateError(itemId: string, error: any): void {
        // Check if it's a 404 error (item was deleted)
        if (error && (error.status === 404 || error.type === Fetcher2.FetcherErrorType.FAILED_404)) {
            this.deleteItem(itemId);
        }
        // For other errors, we'll just log them and continue
        console.warn(`Failed to update item ${itemId}:`, error);
    }
}


export function useLazyDataSourceList<T extends IBasemodel>(
    controller: IBasemodelController<T, any>,
    minItemsPerRequest: number = 50
): LazyDataSourceList<T> {
    const [dataSource] = React.useState(() => new LazyDataSourceList(controller));
    const [request, setRequest] = React.useState<{ count: number; next?: string }>({ count: minItemsPerRequest });

    // Configure data source
    React.useEffect(() => {
        dataSource.setMinItemsPerRequest(minItemsPerRequest);
    }, [dataSource, minItemsPerRequest]);

    // SWR hook for data fetching
    const { data, isLoading, mutate } = controller.useList(request);

    // Update data source when SWR data changes
    React.useEffect(() => {
        dataSource.updateFromSWR(data, isLoading, mutate);
    }, [data, isLoading, mutate, dataSource]);

    // Listen for new requests from data source using a callback approach instead of polling
    React.useEffect(() => {
        const handleNewRequest = (newRequest: { count: number; next?: string }) => {
            if (JSON.stringify(newRequest) !== JSON.stringify(request)) {
                setRequest(newRequest);
            }
        };

        // Set up the callback on the data source
        (dataSource as any).requestCallback = handleNewRequest;

        return () => {
            (dataSource as any).requestCallback = undefined;
        };
    }, [dataSource, request]);

    return dataSource;
}