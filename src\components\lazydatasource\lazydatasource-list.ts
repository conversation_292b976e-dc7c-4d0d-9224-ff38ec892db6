import { mergeObjectsDeep } from "@/functions/object-helper";
import { ItemUpdateStrategy, LazyDataSource } from "./lazydatasource";
import { Fetcher2 } from "@/functions/fetcher2";
import { ItemMutation, MutationOperation } from "./lazymutation";
import { IBasemodel, IBasemodelController, ListResponse } from "@/models/base-model";
import React from "react";

// LazyDataSourceList implementation using IBasemodelController
export class LazyDataSourceList<T extends IBasemodel> implements LazyDataSource<T> {
    private controller: IBasemodelController<T, any>;
    private data: T[] = [];
    private totalCount: number = 0;
    private nextToken?: string;
    private loading: boolean = false;
    private minItemsPerRequest: number = 50;
    private autoLoadUntilExhaustion: boolean = false;
    private dataChangeCallback?: (data: T[]) => void;
    private loadingChangeCallback?: (isLoading: boolean) => void;
    private itemsValidatingChangeCallback?: (isValidating: boolean, validatingIds: string[]) => void;
    private hasInitialLoad: boolean = false;
    private exhausted: boolean = false;
    private currentRequest?: { count: number; nextToken?: string };
    private mutateFunction?: any;

    // Item update management
    private shouldInvalidatePredicate?: (item: T) => boolean;
    private itemUpdateInterval: number = 10000;
    private itemUpdateStrategy: ItemUpdateStrategy = ItemUpdateStrategy.REPLACE;
    private itemUpdateCycleActive: boolean = false;
    private itemsToUpdate: Set<string> = new Set();
    private updatingItems: boolean = false;
    private updateCycleTimeout?: NodeJS.Timeout;

    // Pagination management
    private pageSize: number = 10;
    private currentPage: number = 1;

    constructor(controller: IBasemodelController<T, any>) {
        this.controller = controller;
    }

    // Method to be called from component with SWR result
    updateFromSWR(data: ListResponse<T> | undefined, isLoading: boolean, mutate: any): void {
        this.mutateFunction = mutate;

        if (isLoading !== this.loading) {
            this.loading = isLoading;
            this.loadingChangeCallback?.(isLoading);
        }

        if (data) {
            if (!this.hasInitialLoad) {
                // First load - set total count and reset data
                this.totalCount = data.total;
                this.hasInitialLoad = true;
                this.data = [...data.entries];
            } else if (this.currentRequest) {
                // Subsequent loads - append data only if this was a requested load
                this.data.push(...data.entries);
                this.currentRequest = undefined;
            }

            this.nextToken = data.next;

            if (!this.nextToken) {
                this.exhausted = true;
                // Update total count to match actual loaded data if no more data
                this.totalCount = this.data.length;
            }

            this.notifyDataChange();

            // Auto-load if enabled and not exhausted
            if (this.autoLoadUntilExhaustion && !this.exhausted && !isLoading) {
                setTimeout(() => this.loadMore(), 0);
            }
        }
    }

    getCurrentRequest(): { count: number; next?: string } | undefined {
        return this.currentRequest;
    }

    getData(): T[] {
        return this.data;
    }

    getTotalCount(): number {
        return this.totalCount;
    }

    getLoadedCount(): number {
        return this.data.length;
    }

    isLoading(): boolean {
        return this.loading;
    }

    isUpdatingItems(): boolean {
        return this.updatingItems;
    }

    private notifyDataChange(): void {
        this.dataChangeCallback?.(this.data);
    }

    async loadMore(): Promise<void> {
        if (this.loading || this.exhausted || this.currentRequest) return;

        this.currentRequest = {
            count: this.minItemsPerRequest,
            nextToken: this.nextToken
        };
    }

    async loadUntilPage(page: number, pageSize: number): Promise<void> {
        const requiredItems = page * pageSize;
        
        while (this.data.length < requiredItems && !this.exhausted && !this.currentRequest) {
            await this.loadMore();
            // Wait a bit for the request to be processed
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    async loadAll(): Promise<void> {
        this.setAutoLoadUntilExhaustion(true);
    }

    invalidate(): void {
        this.data = [];
        this.totalCount = 0;
        this.nextToken = undefined;
        this.hasInitialLoad = false;
        this.exhausted = false;
        this.currentRequest = undefined;
        this.notifyDataChange();
        
        // Trigger SWR revalidation if available
        if (this.mutateFunction) {
            this.mutateFunction();
        }
    }

    replaceItem(item: T): void {
        const id = this.controller.getId(item);
        const index = this.data.findIndex(existing => this.controller.getId(existing) === id);
        
        if (index >= 0) {
            this.data[index] = item;
        } else {
            this.data.push(item);
        }
        this.notifyDataChange();
    }

    mergeItem(item: Partial<T>): void {
        // Find item by trying to get ID from the partial item
        let id: string;
        try {
            id = this.controller.getId(item as T);
        } catch {
            console.error('Cannot merge item without valid ID');
            return;
        }

        const index = this.data.findIndex(existing => this.controller.getId(existing) === id);
        
        if (index >= 0) {
            this.data[index] = mergeObjectsDeep(this.data[index], item);
            this.notifyDataChange();
        }
    }

    deleteItem(id: string): void {
        const index = this.data.findIndex(item => this.controller.getId(item) === id);
        if (index >= 0) {
            this.data.splice(index, 1);
            this.totalCount = Math.max(0, this.totalCount - 1);
            this.notifyDataChange();
        }
    }

    applyMutations(mutations: ItemMutation<T>[]): void {
        mutations.forEach(mutation => {
            switch (mutation.operation) {
                case MutationOperation.REPLACE:
                    if (mutation.item) {
                        this.replaceItem(mutation.item);
                    }
                    break;
                case MutationOperation.MERGE:
                    if (mutation.partialItem) {
                        this.mergeItem(mutation.partialItem);
                    }
                    break;
                case MutationOperation.DELETE:
                    if (mutation.id) {
                        this.deleteItem(mutation.id);
                    }
                    break;
            }
        });
    }

    setMinItemsPerRequest(count: number): void {
        this.minItemsPerRequest = count;
    }

    setAutoLoadUntilExhaustion(enabled: boolean): void {
        this.autoLoadUntilExhaustion = enabled;
        if (enabled && !this.exhausted && !this.loading && !this.currentRequest) {
            this.loadMore();
        }
    }

    setPageSize(pageSize: number): void {
        if (this.pageSize !== pageSize) {
            this.pageSize = pageSize;
            // Check if we need to load more data for the current page
            this.checkAndLoadForCurrentPage();
        }
    }

    getPageSize(): number {
        return this.pageSize;
    }

    getCurrentPage(): number {
        return this.currentPage;
    }

    setCurrentPage(page: number): void {
        if (this.currentPage !== page) {
            this.currentPage = page;
            // Check if we need to load more data for the new page
            this.checkAndLoadForCurrentPage();
        }
    }

    private checkAndLoadForCurrentPage(): void {
        const requiredItems = this.currentPage * this.pageSize;
        if (this.data.length < requiredItems && !this.exhausted) {
            this.loadUntilPage(this.currentPage, this.pageSize);
        }
    }

    onDataChange(callback: (data: T[]) => void): void {
        this.dataChangeCallback = callback;
    }

    onLoadingChange(callback: (isLoading: boolean) => void): void {
        this.loadingChangeCallback = callback;
    }

    onItemsValidatingChange(callback: (isValidating: boolean, validatingIds: string[]) => void): void {
        this.itemsValidatingChangeCallback = callback;
    }

    // Item update management methods
    setShouldInvalidate(predicate: (item: T) => boolean): void {
        this.shouldInvalidatePredicate = predicate;
        this.updateItemsToUpdate();
    }

    setItemUpdateInterval(interval: number): void {
        this.itemUpdateInterval = interval;
    }

    setItemUpdateStrategy(strategy: ItemUpdateStrategy): void {
        this.itemUpdateStrategy = strategy;
    }

    startItemUpdateCycle(): void {
        if (this.itemUpdateCycleActive) return;
        this.itemUpdateCycleActive = true;
        this.scheduleNextUpdateCycle();
    }

    stopItemUpdateCycle(): void {
        this.itemUpdateCycleActive = false;
        if (this.updateCycleTimeout) {
            clearTimeout(this.updateCycleTimeout);
            this.updateCycleTimeout = undefined;
        }
    }

    private updateItemsToUpdate(): void {
        if (!this.shouldInvalidatePredicate) return;

        const newItemsToUpdate = new Set<string>();
        this.data.forEach(item => {
            if (this.shouldInvalidatePredicate!(item)) {
                newItemsToUpdate.add(this.controller.getId(item));
            }
        });

        this.itemsToUpdate = newItemsToUpdate;
    }

    private scheduleNextUpdateCycle(): void {
        if (!this.itemUpdateCycleActive) return;

        const cycleStartTime = Date.now();

        this.updateItemsToUpdate();

        if (this.itemsToUpdate.size === 0) {
            // No items to update, schedule next cycle
            this.updateCycleTimeout = setTimeout(() => {
                this.scheduleNextUpdateCycle();
            }, this.itemUpdateInterval);
            return;
        }

        this.performUpdateCycle(cycleStartTime);
    }

    private async performUpdateCycle(cycleStartTime: number): Promise<void> {
        if (!this.itemUpdateCycleActive || this.itemsToUpdate.size === 0) return;

        this.updatingItems = true;
        this.itemsValidatingChangeCallback?.(true, Array.from(this.itemsToUpdate));

        // The actual item updates will be handled by the component
        // This method just manages the timing and state

        // Calculate wait time for next cycle
        const cycleEndTime = Date.now();
        const cycleElapsedTime = cycleEndTime - cycleStartTime;
        const waitTime = Math.max(1, this.itemUpdateInterval - cycleElapsedTime);

        this.updateCycleTimeout = setTimeout(() => {
            this.scheduleNextUpdateCycle();
        }, waitTime);
    }

    // This method will be called by the component with SWR results
    updateItemFromSWR(itemId: string, data: T[] | undefined, error: any): void {
        if (data && data.length > 0) {
            const updatedItem = data[0];
            this.handleItemUpdate(updatedItem);
        } else if (error) {
            this.handleItemUpdateError(itemId, error);
        }
    }

    // Get the current items that need updating
    getItemsToUpdate(): string[] {
        return Array.from(this.itemsToUpdate);
    }

    // Mark an item as being processed for update
    markItemAsUpdating(itemId: string): void {
        // Remove from items to update since it's being processed
        this.itemsToUpdate.delete(itemId);
    }

    // Complete the current update cycle
    completeUpdateCycle(): void {
        this.updatingItems = false;
        this.itemsValidatingChangeCallback?.(false, []);
    }

    private handleItemUpdate(updatedItem: T): void {
        const itemId = this.controller.getId(updatedItem);
        const index = this.data.findIndex(item => this.controller.getId(item) === itemId);

        if (index >= 0) {
            if (this.itemUpdateStrategy === ItemUpdateStrategy.MERGE) {
                this.data[index] = mergeObjectsDeep(this.data[index], updatedItem);
            } else {
                this.data[index] = updatedItem;
            }
            this.notifyDataChange();
        }
    }

    private handleItemUpdateError(itemId: string, error: any): void {
        // Check if it's a 404 error (item was deleted)
        if (error && (error.status === 404 || error.type === Fetcher2.FetcherErrorType.FAILED_404)) {
            this.deleteItem(itemId);
        }
        // For other errors, we'll just log them and continue
        console.warn(`Failed to update item ${itemId}:`, error);
    }
}


export function useLazyDataSourceList<T extends IBasemodel>(
    controller: IBasemodelController<T, any>,
    minItemsPerRequest: number = 50
): LazyDataSourceList<T> {
    const [dataSource] = React.useState(() => new LazyDataSourceList(controller));
    const [request, setRequest] = React.useState<{ count: number; next?: string }>({ count: minItemsPerRequest });

    // Configure data source
    React.useEffect(() => {
        dataSource.setMinItemsPerRequest(minItemsPerRequest);
    }, [dataSource, minItemsPerRequest]);

    // SWR hook for data fetching
    const { data, isLoading, mutate } = controller.useList(request);

    // Update data source when SWR data changes
    React.useEffect(() => {
        dataSource.updateFromSWR(data, isLoading, mutate);
    }, [data, isLoading, mutate, dataSource]);

    // Listen for new requests from data source
    React.useEffect(() => {
        const checkForNewRequest = () => {
            const newRequest = dataSource.getCurrentRequest();
            if (newRequest && JSON.stringify(newRequest) !== JSON.stringify(request)) {
                setRequest(newRequest);
            }
        };

        const interval = setInterval(checkForNewRequest, 50);
        return () => clearInterval(interval);
    }, [dataSource, request]);

    return dataSource;
}