import { Agent, AgentActions, Agent<PERSON>ontroller, AgentStatus } from "@/models/agent";
import { <PERSON>ert, AutoComplete, AutoCompleteProps, Button, Card, ConfigProvider, Divider, Flex, Form, Segmented, Space, Spin, Table, TableColumnsType, TableProps, Tabs, Tag, theme, Upload, UploadProps } from "antd";
import {
    ProfileOutlined, DatabaseOutlined, EditOutlined,
    RocketOutlined,
    DeleteOutlined,
    TagsOutlined,
    LoadingOutlined,
    ReloadOutlined,
    DisconnectOutlined,
    ApartmentOutlined,
} from '@ant-design/icons';
import AgentDescription from "@/components/agent/agent-description";
import React from "react";
import { AgentKnowledgebaseController } from "@/models/agentknowledgebase";
import { KnowledgeBase, KnowledgebaseController, KnowledgeBaseStatus } from "@/models/knowledgebase";
import { GetKnowledgebaseStatusTag } from "@/components/knowledgebase/knowledgebase-description";
import { useTimeAgo } from "next-timeago";
import { AgentTag } from "@/models/agenttag";

interface AgentViewDrawerParams {
    agent: Agent;
    onEdit: (kb: Agent) => void;
    onDelete: (kb: Agent) => void;
    onDeploy: (kb: Agent) => void;
    onEditBehavior: (agent: Agent) => void;
}

interface AssignForm {
    description: string;
}



const AgentViewDrawer: React.FC<AgentViewDrawerParams> = (params: AgentViewDrawerParams) => {

    const agentController: AgentController = new AgentController();
    const knowledgebaseController: KnowledgebaseController = new KnowledgebaseController();


    const [knowledgebasesState, setKnowledgebasesState] = React.useState('assigned')

    const { data: agentKbsData, isLoading: agentKbsLoading, isValidating: agentKbsValidating, mutate: agentKbsMutate } = knowledgebaseController.useListFromAgent({ count: 10 }, params.agent.agentId);
    const { data: agentTagsData, isLoading: agentTagsLoading, isValidating: agentTagsValidating, mutate: agentTagsMutate } = agentController.useListTags({ count: 10 }, params.agent.agentId);


    const {
        token: { colorPrimary },
    } = theme.useToken();
    const { TimeAgo } = useTimeAgo();

    const agentKbsColumns: TableColumnsType<KnowledgeBase> = [
        {
            title: 'Name', dataIndex: 'name'
        },
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'Status', dataIndex: 'status', render: (status: KnowledgeBaseStatus, record: KnowledgeBase, index) => {
                const text = GetKnowledgebaseStatusTag(status);
                return (
                    <Space size={'small'}>
                        {text}
                    </Space>
                );
            },
        },
        { title: 'Tag', dataIndex: 'tag' },
        {
            title: 'Last Modified', dataIndex: 'lastChangeTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    const agentTagsColumns: TableColumnsType<AgentTag> = [
        {
            title: 'Tag', dataIndex: 'tag'
        },
        {
            title: 'Created', dataIndex: 'createdAt', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    const TODO = false;

    const assignedKnowledgebasesSubTab = (
        <>
            <Flex align="end" justify="flex-end" style={{ width: '100%', marginBottom: '15px' }}>
                <Space size={"middle"} align="end">
                    <Button type="primary" icon={(agentKbsLoading || agentKbsValidating) ? <LoadingOutlined spin /> : <ReloadOutlined />} disabled={(agentKbsLoading || agentKbsValidating)} onClick={() => { mutateKbFiles() }}>Refresh</Button>
                    <Button type="primary">Unassign Selected Agent</Button>
                </Space>
            </Flex>
            <Table<KnowledgeBase>
                dataSource={agentKbsData?.entries}
                loading={agentKbsLoading}
                columns={agentKbsColumns}
                rowSelection={{ type: "radio" }}
                rowKey='fileId'
            />
        </>
    )

    return (
        <Tabs
            size="large"
            items={[
                {
                    key: 'Overview',
                    label: 'Overview',
                    icon: <ProfileOutlined />,
                    children: (
                        <Space direction="vertical" style={{ padding: '0 24px' }}>
                            <AgentDescription layout="vertical" agent={params.agent} />
                            <Divider style={{ color: '#ccc' }}>Actions</Divider>
                            <Flex justify='center' align='center' gap="middle">
                                <Button disabled={!agentController.can(AgentActions.EDIT, params.agent)} type="text" icon={<EditOutlined />} onClick={() => { params.onEdit(params.agent) }}>Edit</Button>
                                <Button disabled={!agentController.can(AgentActions.EDIT_BEHAVIOR, params.agent)} type="text" icon={<ApartmentOutlined />} onClick={() => { params.onEditBehavior(params.agent) }}>Edit Behavior</Button>
                                <Button type="text" icon={<RocketOutlined />} onClick={() => { params.onDeploy(params.agent) }}>Deploy</Button>
                                <Button disabled={!agentController.can(AgentActions.DELETE, params.agent)} type="primary" danger icon={<DeleteOutlined />} onClick={() => { params.onDelete(params.agent) }}>Delete</Button>
                            </Flex>
                        </Space>
                    )
                },
                {
                    key: 'Knowledge Bases',
                    label: 'Knowledge Bases',
                    icon: <DatabaseOutlined />,
                    children: (
                        <>
                            <ConfigProvider
                                theme={{
                                    components: {
                                        Segmented: {
                                            itemSelectedColor: '#fff',
                                            itemSelectedBg: colorPrimary,
                                        },
                                    },
                                }}
                            >
                                <Segmented
                                    options={[{ label: 'Knowledge Bases', value: 'assigned' }, { label: 'Assign Knowledge Base', value: 'assign' }]}
                                    block
                                    onChange={(value) => setKnowledgebasesState(value)}
                                    value={knowledgebasesState} />
                            </ConfigProvider>
                            <Divider />
                            {knowledgebasesState == 'assigned' ? assignedKnowledgebasesSubTab : null}
                        </>
                    )
                },
                {
                    key: 'Tags',
                    label: 'Tags',
                    icon: <TagsOutlined />,
                    children: (
                        <>
                            <Table<AgentTag>
                                dataSource={agentTagsData?.entries}
                                loading={agentTagsLoading}
                                columns={agentTagsColumns}
                                rowKey={(agentTag: AgentTag) => agentTag.AgentId + '::' + agentTag.Tag}
                            />
                        </>
                    )
                }
            ]}
        />
    )
}

export default AgentViewDrawer;