'use client'

import React from 'react';
import { But<PERSON>, Di<PERSON><PERSON>, Drawer, Space, TableColumnsType, Typography } from 'antd';
import {
  PlusOutlined,
} from '@ant-design/icons';
import ListKnowledgeBasesComponent from './list'
import { KnowledgeBase, KnowledgebaseController } from '@/models/knowledgebase';
import PutKnowledgebaseForm from './put-form';
import { DrawerFormTemplateProperties } from '@/templates/drawer-template';
import '@ant-design/v5-patch-for-react-19';
import DataTable from '@/components/data-table';

type KnowledgeComponentProps = any;

type DrawerState = { title: string; isOpen: boolean; drawerParams?: DrawerFormTemplateProperties<KnowledgeBase> };
const emptyDrawer: DrawerState = { title: "", isOpen: false, drawerParams:{isOpen: false} }


const columns: TableColumnsType<KnowledgeBase> = [
  {
      title: 'Name', dataIndex: 'name'
  },
  { title: 'Description', dataIndex: 'description' }
]


const KnowledgeComponent: React.FC<KnowledgeComponentProps> = (props: KnowledgeComponentProps) => {
  const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
  const [mutateObject, setMutateObject] = React.useState<KnowledgeBase[]>([]);

  const onCloseDrawer = () => {
    setDrawerOptions(emptyDrawer);
  };

  return (
    <>
      <Typography.Title level={2}>
        Knowledge Bases
      </Typography.Title>
      <Button size='large' type='primary' onClick={() => {
          setDrawerOptions(
            {
              title: "Create Knowledge Base",
              isOpen: true,
              drawerParams: {
                isOpen: true,
                title: "Create a Knowledge Base",
                onSuccess: (data : KnowledgeBase) => { setDrawerOptions(emptyDrawer); setMutateObject([data]); },
                mode: 'create',
                data : null
              }
            }
          )
      }}><PlusOutlined />Create</Button>
      <Divider />
      <ListKnowledgeBasesComponent onClick={(record: KnowledgeBase) => {
        setDrawerOptions(
          {
            title: "Edit a Knowledge Base",
            isOpen: true,
            drawerParams: {
              isOpen: true,
              title: `Editing '${record.name}'`,
              onSuccess: (data : KnowledgeBase) => { setDrawerOptions(emptyDrawer); setMutateObject([data]);  },
              mode: 'edit',
              data: record
            }
          }
        )
      }} mutateItems={mutateObject} />
      <Divider/>
      <DataTable<KnowledgeBase, KnowledgebaseController>
        controller={new KnowledgebaseController()} 
        itemUpdateInterval={10000} 
        tableColumns={columns} 
        shouldInvalidate={function (entry: KnowledgeBase): boolean {
          return false;
        } }        
      />
      <Drawer
        title={drawerOptions.title}
        placement="right"
        size="large"
        onClose={onCloseDrawer}
        open={drawerOptions.isOpen}
        extra={
          <Space>
            <Button onClick={onCloseDrawer}>Cancel</Button>
          </Space>
        }
      >
        <PutKnowledgebaseForm {...drawerOptions.drawerParams} />
      </Drawer>
    </>
  );
}

export default KnowledgeComponent;