import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export interface Claim{
    type : string;
    value : string;
}

export interface Api<PERSON>ey extends IBasemodel{
    name : string;
    key : string;
    secret : string;
    claim: Claim[];
    paused : boolean;
    expireAt: number;
    OwnerUserId : string;
}

export enum ApiKeyActions {
    DELETE=2,
    PAUSE=4,
    RESUME=8
}

export interface ApiKeyCreateRequest{
    name : string;
    expireAt : number;
    claims: Claim[];
    personalKey : boolean;
}

export class ApiKeyController implements IBasemodelController<ApiKey, ApiKeyActions>{
    getId = (item: ApiKey) => item.key;
    useList = (request: ListRequest) => Fetcher2.SWRTemplate<ListResponse<ApiKey>>(APIRoutes.ApiKeyController.LIST, {method: 'GET', queryString: request})
    useDelete = (apiKey : string) => Fetcher2.SWRMutationTemplate<ApiKey>(APIRoutes.ApiKeyController.DELETE, {method: 'DELETE', urlPlaceholders: {apiKey: apiKey}})
    
    useGet = (ids: string[], options?: SWRConfiguration) => Fetcher2.SWRMultiTemplate<ApiKey>(ids.map((id) => APIRoutes.ApiKeyController.GET.replace("{apiKey}", id)), { method: 'GET' }, options)
    
    can = (action: ApiKeyActions, onItem: ApiKey) => {
        switch(action){
            case ApiKeyActions.DELETE:
                return onItem.paused || onItem.expireAt >= Math.floor(Date.now()/1000);
            case ApiKeyActions.PAUSE:
                return !onItem.paused && onItem.expireAt > Math.floor(Date.now()/1000);
            case ApiKeyActions.RESUME:
                return onItem.paused && onItem.expireAt > Math.floor(Date.now()/1000);
            default:
                return false;
        }
    }

    useCreate = () => Fetcher2.SWRMutationTemplate<ApiKey>(APIRoutes.ApiKeyController.CREATE, {method: 'POST'})
}