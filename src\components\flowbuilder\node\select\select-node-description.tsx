import { Tag } from "antd";
import { SelectNodeModelData } from "./select-node-model";
import { BaseNodeData } from "../base-node";

function SelectNodeDescription(props: BaseNodeData<SelectNodeModelData>) {
    return (
        <>
            Select execution with <Tag color="blue-inverse" bordered={false}>{props.nodeData.outputCount}</Tag> outputs
        </>
    );
}

export default SelectNodeDescription;
