import DrawerTemplate, { DrawerTemplateErrorEmpty } from "@/templates/drawer-template";
import { Button, Card, Divider, Form, Input, Select, Space, Typography, message } from "antd";
import React, { useEffect, useState } from "react";
import '@ant-design/v5-patch-for-react-19';
import SubmitButton from "@/components/submit-button";
import { AgentTool, AgentToolController, AgentToolCreateRequest, AgentToolLanguage, AgentToolUpdateRequest } from "@/models/agenttool";
import { CodeBlock, dracula } from 'react-code-blocks';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { AgentToolInput } from "@/models/agenttool";

interface AgentToolEditDrawerParams {
    isOpen: boolean;
    agentTool?: AgentTool;
    mode: 'create' | 'edit';
    onSuccess: (data: AgentTool) => void;
}

const AgentToolEditDrawer: React.FC<AgentToolEditDrawerParams> = (params: AgentToolEditDrawerParams) => {
    const agentToolController: AgentToolController = new AgentToolController();
    const [form] = Form.useForm<AgentTool>();
    const [drawerError, setDrawerError] = React.useState(DrawerTemplateErrorEmpty);
    const { data: createData, trigger: createTrigger, isMutating: isCreating } = agentToolController.useCreate();
    const { data: updateData, trigger: updateTrigger, isMutating: isUpdating } = agentToolController.useUpdate();
    const [code, setCode] = useState('');
    const [language, setLanguage] = useState<AgentToolLanguage>(AgentToolLanguage.PYTHON);
    const [inputs, setInputs] = useState<AgentToolInput[]>([]);

    const isMutating = isCreating || isUpdating;

    useEffect(() => {
        const data = createData || updateData;
        if (data === undefined) return;
        setDrawerError(DrawerTemplateErrorEmpty);
        if (params.onSuccess != null) params.onSuccess(data);
    }, [createData, updateData]);

    useEffect(() => {
        if (params.isOpen) {
            form.resetFields();
            setDrawerError(DrawerTemplateErrorEmpty);
            if (params.mode === 'edit' && params.agentTool) {
                form.setFieldsValue({
                    name: params.agentTool.name,
                    description: params.agentTool.description,
                    language: params.agentTool.language
                });
                setCode(params.agentTool.code);
                setLanguage(params.agentTool.language);
                setInputs(params.agentTool.inputs || []);
            } else {
                setCode(getDefaultCode(language));
                setInputs([]);
            }
        }
    }, [params.isOpen, params.agentTool, params.mode]);

    const getDefaultCode = (lang: AgentToolLanguage): string => {
        switch (lang) {
            case AgentToolLanguage.PYTHON:
                return `def handler(event, context):
    """
    AWS Lambda handler function
    
    Args:
        event: Input parameters from the tool invocation
        context: AWS Lambda context object
    
    Returns:
        dict: Response object with result
    """
    # Your code here
    return {
        'statusCode': 200,
        'body': 'Hello from Python!'
    }`;
            case AgentToolLanguage.JAVA:
                return `package com.example;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import java.util.Map;

public class Handler implements RequestHandler<Map<String, Object>, Map<String, Object>> {
    
    @Override
    public Map<String, Object> handleRequest(Map<String, Object> event, Context context) {
        // Your code here
        return Map.of("statusCode", 200, "body", "Hello from Java!");
    }
}`;
            case AgentToolLanguage.CSHARP:
                return `using Amazon.Lambda.Core;
using System.Collections.Generic;

[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.SystemTextJson.DefaultLambdaJsonSerializer))]

namespace Example
{
    public class Function
    {
        public Dictionary<string, object> FunctionHandler(Dictionary<string, object> input, ILambdaContext context)
        {
            // Your code here
            return new Dictionary<string, object>
            {
                ["statusCode"] = 200,
                ["body"] = "Hello from C#!"
            };
        }
    }
}`;
            default:
                return '';
        }
    };

    const onLanguageChange = (newLanguage: AgentToolLanguage) => {
        setLanguage(newLanguage);
        if (!code || code === getDefaultCode(language)) {
            setCode(getDefaultCode(newLanguage));
        }
    };

    const addInput = () => {
        const newInput: AgentToolInput = {
            id: crypto.randomUUID(),
            name: '',
            dataType: 'STRING',
            required: false,
            defaultValue: '',
            description: ''
        };
        setInputs([...inputs, newInput]);
    };

    const removeInput = (index: number) => {
        setInputs(inputs.filter((_, i) => i !== index));
    };

    const updateInput = (index: number, field: keyof AgentToolInput, value: any) => {
        const newInputs = [...inputs];
        newInputs[index] = { ...newInputs[index], [field]: value };
        setInputs(newInputs);
    };

    const onFinish = async (values: any) => {
        try {
            if (params.mode === 'create') {
                const request: AgentToolCreateRequest = {
                    name: values.name,
                    description: values.description,
                    language: language,
                    code: code,
                    inputs: inputs
                };
                await createTrigger(request);
            } else if (params.agentTool) {
                const request: AgentToolUpdateRequest = {
                    id: params.agentTool.id,
                    name: values.name,
                    description: values.description,
                    language: language,
                    code: code,
                    inputs: inputs
                };
                await updateTrigger(request);
            }
        } catch (error) {
            message.error('Failed to save agent tool');
        }
    };

    return (
        <DrawerTemplate
            error={drawerError}
            isLoading={isMutating}
            title={(params.mode === 'create') ? 'Create Agent Tool' : 'Edit ' + params.agentTool?.name}
        >
            <Form form={form} layout='vertical' onFinish={onFinish} scrollToFirstError={{ behavior: 'instant', block: 'end', focus: true }}>
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[
                        { max: 64, message: 'Please input up to 64 characters.' },
                        { min: 3, message: 'Tool name must have at least 3 characters.' },
                        { required: true, message: 'Please input a name for the tool' }
                    ]}
                >
                    <Input />
                </Form.Item>
                
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[
                        { max: 250, message: 'Please input a description of up to 250 characters' },
                        { required: true, message: 'Please input a description' }
                    ]}
                >
                    <Input.TextArea rows={3} count={{ show: true, max: 250 }} />
                </Form.Item>

                <Form.Item
                    name="language"
                    label="Programming Language"
                    rules={[{ required: true, message: 'Please select a programming language' }]}
                >
                    <Select value={language} onChange={onLanguageChange}>
                        <Select.Option value={AgentToolLanguage.PYTHON}>Python</Select.Option>
                        <Select.Option value={AgentToolLanguage.JAVA}>Java</Select.Option>
                        <Select.Option value={AgentToolLanguage.CSHARP}>C#</Select.Option>
                    </Select>
                </Form.Item>

                <Form.Item label="Code">
                    <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', overflow: 'hidden' }}>
                        <textarea
                            value={code}
                            onChange={(e) => setCode(e.target.value)}
                            style={{
                                width: '100%',
                                minHeight: '300px',
                                border: 'none',
                                padding: '12px',
                                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                fontSize: '14px',
                                resize: 'vertical'
                            }}
                            placeholder="Enter your code here..."
                        />
                    </div>
                </Form.Item>

                <Divider />

                <Form.Item label="Input Parameters">
                    <Space direction="vertical" style={{ width: '100%' }}>
                        {inputs.map((input, index) => (
                            <Card key={input.id} size="small" style={{ marginBottom: 8 }}>
                                <Space direction="vertical" style={{ width: '100%' }}>
                                    <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                                        <Typography.Text strong>Input {index + 1}</Typography.Text>
                                        <Button
                                            type="text"
                                            danger
                                            icon={<DeleteOutlined />}
                                            onClick={() => removeInput(index)}
                                        />
                                    </Space>
                                    <Space style={{ width: '100%' }}>
                                        <Input
                                            placeholder="Parameter name"
                                            value={input.name}
                                            onChange={(e) => updateInput(index, 'name', e.target.value)}
                                            style={{ width: 150 }}
                                        />
                                        <Select
                                            value={input.dataType}
                                            onChange={(value) => updateInput(index, 'dataType', value)}
                                            style={{ width: 120 }}
                                        >
                                            <Select.Option value="STRING">String</Select.Option>
                                            <Select.Option value="INTEGER">Integer</Select.Option>
                                            <Select.Option value="FLOAT">Float</Select.Option>
                                            <Select.Option value="BOOLEAN">Boolean</Select.Option>
                                            <Select.Option value="JSON">JSON</Select.Option>
                                        </Select>
                                        <Input
                                            placeholder="Default value"
                                            value={input.defaultValue}
                                            onChange={(e) => updateInput(index, 'defaultValue', e.target.value)}
                                            style={{ width: 120 }}
                                        />
                                        <Button
                                            type={input.required ? "primary" : "default"}
                                            onClick={() => updateInput(index, 'required', !input.required)}
                                        >
                                            {input.required ? 'Required' : 'Optional'}
                                        </Button>
                                    </Space>
                                    <Input
                                        placeholder="Description (optional)"
                                        value={input.description}
                                        onChange={(e) => updateInput(index, 'description', e.target.value)}
                                    />
                                </Space>
                            </Card>
                        ))}
                        <Button type="dashed" onClick={addInput} icon={<PlusOutlined />} style={{ width: '100%' }}>
                            Add Input Parameter
                        </Button>
                    </Space>
                </Form.Item>

                <Form.Item>
                    <SubmitButton form={form}>
                        {(params.mode === 'create') ? 'Create Tool' : 'Save Changes'}
                    </SubmitButton>
                </Form.Item>
            </Form>
        </DrawerTemplate>
    );
};

export default AgentToolEditDrawer;
