import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "../base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export interface Flow extends IBasemodel {
    id: string;
    name: string;
    description: string;
    currentTagId?: string;
    flowDefinition?: string;
}

export enum FlowActions {
    EDIT,
    DELETE,
    CREATE,
    FLOWBUILDER,
    CREATE_TAG,
    CREATE_ALIAS
}

export interface FlowCreateRequest {
    name: string;
    description: string;
}

export interface FlowUpdateRequest {
    id: string;
    name: string;
    description: string;
    flowDefinition?: string;
}

export class FlowController implements IBasemodelController<Flow, FlowActions> {
    getId: (item: Flow) => string = (item) => item.id;

    useGet = (ids: string[], options?: SWRConfiguration) =>
        Fetcher2.SWRMultiTemplate<Flow>(
            ids.map((id) => APIRoutes.FlowController.GET.replace("{id}", id)),
            { method: 'GET' },
            options
        )

    can = (action: FlowActions, onItem: Flow) => {
        return true;
    }

    useList = (request: ListRequest) =>
        Fetcher2.SWRTemplate<ListResponse<Flow>>(
            APIRoutes.FlowController.LIST,
            {method: 'GET', queryString: request}
        )

    useDelete = (id: string) =>
        Fetcher2.SWRMutationTemplate<Flow>(
            APIRoutes.FlowController.DELETE,
            {method: 'DELETE', urlPlaceholders: {id: id}}
        )

    useCreate = () =>
        Fetcher2.SWRMutationTemplate<Flow>(
            APIRoutes.FlowController.CREATE,
            {method: 'POST'}
        )

    useUpdate = () =>
        Fetcher2.SWRMutationTemplate<Flow>(
            APIRoutes.FlowController.EDIT,
            {method: 'PUT'}
        )
}
