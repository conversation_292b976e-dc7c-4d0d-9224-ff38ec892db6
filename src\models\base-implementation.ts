import { Fetcher } from "@/functions/fetcher";
import { DeleteMultiRequest, DeleteMultiRequestResponse, IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { SWRConfiguration } from "swr";

export class BaseModelImplementation<T extends IBasemodel> {

    useGet = (endpoint: string, ids: string[], options: SWRConfiguration = {}) => {
        const { data, error, isLoading, isValidating, mutate } = Fetcher.SWRFetcherMulti<T>(
            ids.map((id: string) => endpoint.replace("{id}", id)),
            {
                method: 'GET',
                auth: true,
            },
            {
                errorRetryCount: 0,
                revalidateOnFocus: false,
                revalidateOnReconnect: false,
                revalidateIfStale: false,
                ...options
            })
        return {
            data: data,
            error: !!error,
            isValidating: isValidating,
            isLoading: (!data && !error) || isLoading,
            mutate
        }
    };

    useGenericGet = <T extends object>(endpoint: string, options: SWRConfiguration = {}, endpointPlaceholders : object = {}) => {
        Object.entries(endpointPlaceholders).forEach(([placeholder, value]) => {endpoint.replace("{" + placeholder + "}", value)});

        const { data, error, trigger, isMutating } = Fetcher.SWRMutation<T>(
            endpoint, 
            { 
                method: 'GET', 
                auth: true
            }, 
            { 
            })
    
        return {
            data: data,
            error: !!error,
            trigger,
            isMutating: isMutating
        }
    };

    useSearch = (endpoint: string, query: string | undefined, limit: number = 1) => {
        const { data, error, isLoading, isValidating, mutate } = Fetcher.fetcherSWR<ListResponse<T>>(
            endpoint,
            {
                method: 'GET',
                auth: true,
                queryString: { query: query?.toLowerCase(), limit: limit }
            },
            {
                errorRetryCount: 3,
                revalidateOnFocus: false,
                revalidateOnReconnect: false,
                keepPreviousData: true,
                isPaused: () => { return query !== undefined && query.length == 0 }
            })

        return {
            data: data,
            error: !!error,
            isValidating: isValidating,
            isLoading: (!data && !error) || isLoading,
            mutate
        }
    }

    useList = (endpoint : string, request: ListRequest, endpointPlaceholders : object = {}) => {
        Object.entries(endpointPlaceholders).forEach(([placeholder, value]) => {endpoint.replace("{" + placeholder + "}", value)});

        const { data, error, isLoading, isValidating, mutate } = Fetcher.fetcherSWR<ListResponse<T>>(    
            endpoint, 
            { 
                method: 'GET', 
                auth: true, 
                queryString: request 
            }, 
            { 
                errorRetryCount: 3,
                revalidateOnFocus: false,
                revalidateOnReconnect: false,
            })
    
        return {
            data: data,
            error: !!error,
            isValidating: isValidating,
            isLoading: (!data && !error) || isLoading,
            mutate
        }
    }


    useDeleteMulti = (endpoint : string) => {
        const { data, error, trigger, isMutating } = Fetcher.SWRMutation<DeleteMultiRequest>(
            endpoint, 
            { 
                method: 'DELETE', 
                auth: true
            }, 
            { 
            })
    
        return {
            data: data,
            error: !!error,
            trigger,
            isMutating: isMutating
        }
    }


    usePost = <TRequest extends object>(endpoint : string, endpointPlaceholders : object = {}) => {
        Object.entries(endpointPlaceholders).forEach(([placeholder, value]) => {endpoint.replace("{" + placeholder + "}", value)});

        const { data, error, trigger, isMutating } = Fetcher.SWRMutation<TRequest>(
            endpoint, 
            { 
                method: 'POST', 
                auth: true
            }, 
            { 
            })
    
        return {
            data: data,
            error: !!error,
            trigger,
            isMutating: isMutating
        }
    }

}