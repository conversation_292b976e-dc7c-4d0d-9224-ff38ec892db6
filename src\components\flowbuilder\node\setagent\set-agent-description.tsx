import { Tag } from "antd";
import { SetAgentNodeModelData } from "./set-agent-model";
import { BaseNodeData } from "../base-node";

function SetAgentNodeDescription(props: BaseNodeData<SetAgentNodeModelData>) {
    return (
      <>
        Set agent to <Tag color="blue-inverse" bordered={false}>{props.nodeData.agentId ?? '?'}</Tag>{(props.nodeData.isAlias ? 'alias' : 'tag')} <Tag color="blue-inverse" bordered={false}>{props.nodeData.agentAliasOrTag ?? '?'}</Tag>
      </>
    );
  }
  
  export default SetAgentNodeDescription;