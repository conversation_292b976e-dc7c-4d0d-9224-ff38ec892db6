"use client"

import { createContext, useContext, useState, ReactNode } from 'react';
import { NodeTypes } from './types';

/*
export type NodeDataType<T> = { [K in keyof T]: any };
export type DnDContextType<T> = [string | null, (type: NodeDataType<T> | null) => void];
const CreateDnDContext = <T,>() => {
  const DnDContext = createContext<DnDContextType<T>>([null, (_: NodeDataType<T> | null) => {}]);
  return DnDContext;
}*/

export type DnDContextType<T> = [string | null, (type: T | null) => void];
const CreateDnDContext = <T,>() => {
  const DnDContext = createContext<DnDContextType<T>>([null, (_: T | null) => {}]);
  return DnDContext;
}

export interface DnDProviderProps<T> {
  children: ReactNode;
  context: React.Context<DnDContextType<T>>;
}

export const DnDProvider = <T,>({ children, context }: DnDProviderProps<T>) => {
  const [type, setType] = useState<T | null>(null);

  return (
    <context.Provider value={[type as string | null, setType]}>
      {children}
    </context.Provider>
  );
}
 
export default CreateDnDContext;
 
export const useDnDContext = <T extends any>(context: React.Context<DnDContextType<T>>) => {
  return useContext(context);
}