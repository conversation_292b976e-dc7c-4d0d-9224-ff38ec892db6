'use client'

import { QueryBuilderDnD } from '@react-querybuilder/dnd';
import * as ReactDnD from 'react-dnd';
import * as ReactDndHtml5Backend from 'react-dnd-html5-backend';
import { QueryBuilderAntD } from '@react-querybuilder/antd';
import { useState } from 'react';
import { type Field, OptionGroup, QueryBuilder, type RuleGroupType } from 'react-querybuilder';
import '@ant-design/v5-patch-for-react-19';
import "./condition.css";
import { Button, Divider, Space } from 'antd';
import ConditionDescription from './condition-description';


interface ConditionProps{
    fields : OptionGroup<Field>[];
    query?: RuleGroupType;
    onChange: (query : RuleGroupType) => void;
    onCancel: (newConditions? : RuleGroupType) => void;
}

export default function Condition(props : ConditionProps) {
    const [query, setQuery] = useState<RuleGroupType>(structuredClone(props.query) ?? { combinator: 'and', rules: [] });

    return (
        <Space direction='vertical' style={{width:'100%'}} size={'large'}>
            <QueryBuilderDnD dnd={{ ...ReactDnD, ...ReactDndHtml5Backend }}>
                <QueryBuilderAntD>
                    <QueryBuilder fields={props.fields} defaultQuery={query} onQueryChange={setQuery} />
                </QueryBuilderAntD>
            </QueryBuilderDnD>
            <ConditionDescription fields={props.fields} contitionData={query} />
            <Divider />
            <Space direction='horizontal'>
                <Button onClick={() => {props.onChange(query); props.onCancel()}}>Save Changes</Button>
                <Button onClick={() => props.onCancel()}>Cancel</Button>
            </Space>
        </Space>
    );
}