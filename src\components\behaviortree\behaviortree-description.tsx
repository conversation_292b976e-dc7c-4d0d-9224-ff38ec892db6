import { BehaviorTree } from "@/models/behaviortree"
import { Descriptions, DescriptionsProps, Skeleton } from "antd";
import { useTimeAgo } from "next-timeago";

export interface BehaviorTreeDescriptionParams {
    locale?: string,
    behaviorTree?: BehaviorTree,
    layout?: 'vertical' | 'horizontal';
}

const BehaviorTreeDescription: React.FC<BehaviorTreeDescriptionParams> = (params) => {
    const { TimeAgo } = useTimeAgo();

    const BehaviorTreeDescriptionFields: DescriptionsProps['items'] = (params.behaviorTree) ? [
        {
            key: 'name',
            label: 'Name',
            children: params.behaviorTree.name,
        },
        {
            key: 'description',
            label: 'description',
            children: params.behaviorTree.description,
            span: 2,
        },
        {
            key: 'Created At',
            label: 'Created At',
            children: (<TimeAgo date={params.behaviorTree.createdAt * 1000} locale="en-US" live={true} />),
        },
        {
            key: 'Last Modified',
            label: 'Last Modified',
            children: (<TimeAgo date={params.behaviorTree.lastChangeTimestamp * 1000} locale="en-US" live={true} />),
        },
    ].filter(item => item.children !== undefined) : [];



    return ((params.behaviorTree) ? <Descriptions items={BehaviorTreeDescriptionFields} layout={params.layout ?? 'horizontal'}></Descriptions> : <Skeleton />)
}

export default BehaviorTreeDescription;