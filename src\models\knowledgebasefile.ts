import { KeyedMutator, SWRConfiguration } from "swr";
import { DeleteMultiRequest, DeleteMultiRequestResponse, DeleteMultiResponse, IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { BaseModelImplementation } from "./base-implementation";
import { APIRoutes } from "@/constants";
import { Fetcher } from "@/functions/fetcher";
import { Agent } from "./agent";
import { Fetcher2 } from "@/functions/fetcher2";

export enum KnowledgeBaseFileUploadStatus
{
    QUEUED = 'QUEUED',
    UPLOADING = 'UPLOADING',
    DELETED = 'DELETED',
    READY = 'READY'
}

export interface KnowledgebaseFile extends IBasemodel {
    fileId : string ;
    fileName : string;
    uploadedFileSize : number;
    isDeployed : boolean;
    status : KnowledgeBaseFileUploadStatus;
}

export interface KbFilesListRequest extends ListRequest{
    kbId : string;
}

export interface KnowledgeBaseFileUploadResponse{
    successfulFiles : KnowledgebaseFile[];
}

export class KnowledgebaseFileController implements IBasemodelController<KnowledgebaseFile> {
    getIdFieldName = () => "fileId";
    getId = (item: KnowledgebaseFile) => item.fileId;
    
    useList = (request: KbFilesListRequest) => Fetcher2.SWRTemplate<ListResponse<KnowledgebaseFile>>(
        APIRoutes.KnowledgeBaseFileController.LIST_FILES_FOR_KNOWLEDGEBASE, {method:'GET', queryString: request}
    )

    getDownload : (fileId : string, fileName : string) => Promise<any> = (fileId : string, fileName : string) => {
        return Fetcher2.DownloadTemplate(
            APIRoutes.KnowledgeBaseFileController.DOWNLOAD_KBFILE,
            {
                auth: true,
                method:'GET',
                urlPlaceholders: {fileId: fileId}
            },
            fileName
        )
    }

    getUploadURL : (kbId : string) => string = (kbId : string) =>{
        return APIRoutes.GetURLString(APIRoutes.KnowledgeBaseFileController.UPLOAD.replace("{kbId}", kbId))
    }

    getUploadHeaders : () => object = () =>{
        return Fetcher.GetAuthHeader()
    }

    useDeleteMulti = () => Fetcher2.SWRMutationTemplate<DeleteMultiResponse>(APIRoutes.KnowledgeBaseFileController.DELETE_MULTI, {method:'DELETE'}, {})


/*
    implementation = new BaseModelImplementation<KnowledgebaseFile>();

    useDeleteMulti2 = () => this.implementation.useDeleteMulti(APIRoutes.GetURLString(APIRoutes.KnowledgeBaseFileController.DELETE_MULTI))*/
}