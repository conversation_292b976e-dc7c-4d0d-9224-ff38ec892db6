{"openapi": "3.0.3", "info": {"title": "Coral Agents - BehaviorTree API", "description": "API endpoints for managing agent behavior trees in the Coral Agents platform", "version": "1.0.0"}, "servers": [{"url": "http://localhost:5083", "description": "Development server"}], "paths": {"/agentbehaviortree": {"get": {"summary": "List behavior trees", "description": "Retrieve a list of behavior trees, optionally filtered by agent ID", "parameters": [{"name": "count", "in": "query", "description": "Number of items to return", "schema": {"type": "integer", "default": 10}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "schema": {"type": "integer", "default": 0}}, {"name": "agentId", "in": "query", "description": "Filter behavior trees by agent ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "List of behavior trees", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BehaviorTreeListResponse"}}}}}}, "post": {"summary": "Create behavior tree", "description": "Create a new behavior tree for an agent", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BehaviorTreeCreateRequest"}}}}, "responses": {"201": {"description": "Behavior tree created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BehaviorTree"}}}}, "400": {"description": "Invalid request data"}}}}, "/agentbehaviortree/{id}": {"get": {"summary": "Get behavior tree by ID", "description": "Retrieve a specific behavior tree by its ID", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Behavior tree ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Behavior tree details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BehaviorTree"}}}}, "404": {"description": "Behavior tree not found"}}}, "put": {"summary": "Update behavior tree", "description": "Update an existing behavior tree", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Behavior tree ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BehaviorTreeUpdateRequest"}}}}, "responses": {"200": {"description": "Behavior tree updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BehaviorTree"}}}}, "404": {"description": "Behavior tree not found"}, "400": {"description": "Invalid request data"}}}, "delete": {"summary": "Delete behavior tree", "description": "Delete a behavior tree by its ID", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Behavior tree ID", "schema": {"type": "string"}}], "responses": {"204": {"description": "Behavior tree deleted successfully"}, "404": {"description": "Behavior tree not found"}}}}}, "components": {"schemas": {"BehaviorTree": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the behavior tree"}, "name": {"type": "string", "description": "Name of the behavior tree"}, "description": {"type": "string", "description": "Description of the behavior tree"}, "agentId": {"type": "string", "description": "ID of the agent this behavior tree belongs to", "nullable": true}, "treeDefinition": {"type": "string", "description": "JSON string containing the flow definition (nodes, edges, fields)", "nullable": true}, "createdAt": {"type": "integer", "description": "Creation timestamp"}, "lastChangeTimestamp": {"type": "integer", "description": "Last modification timestamp"}}, "required": ["id", "name", "description"]}, "BehaviorTreeCreateRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the behavior tree"}, "description": {"type": "string", "description": "Description of the behavior tree"}, "agentId": {"type": "string", "description": "ID of the agent this behavior tree belongs to", "nullable": true}, "treeDefinition": {"type": "string", "description": "JSON string containing the flow definition", "nullable": true}}, "required": ["name", "description"]}, "BehaviorTreeUpdateRequest": {"type": "object", "properties": {"id": {"type": "string", "description": "Behavior tree ID"}, "name": {"type": "string", "description": "Name of the behavior tree"}, "description": {"type": "string", "description": "Description of the behavior tree"}, "agentId": {"type": "string", "description": "ID of the agent this behavior tree belongs to", "nullable": true}, "treeDefinition": {"type": "string", "description": "JSON string containing the flow definition", "nullable": true}}, "required": ["id", "name", "description"]}, "BehaviorTreeListResponse": {"type": "object", "properties": {"entries": {"type": "array", "items": {"$ref": "#/components/schemas/BehaviorTree"}}, "totalCount": {"type": "integer", "description": "Total number of behavior trees"}}, "required": ["entries", "totalCount"]}}}}