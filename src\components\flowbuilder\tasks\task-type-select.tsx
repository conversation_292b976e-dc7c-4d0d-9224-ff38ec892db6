import React from 'react';
import { But<PERSON>, Select, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

export interface TaskTypeOption {
    label: string;
    value: string;
}

interface TaskTypeSelectProps {
    value: string | null;
    onChange: (value: string | null) => void;
    onAddTask: () => void;
    options: TaskTypeOption[];
    selectWidth?: number;
}

const TaskTypeSelect: React.FC<TaskTypeSelectProps> = ({
    value,
    onChange,
    onAddTask,
    options,
    selectWidth = 200
}) => {
    return (
        <Space>
            <Select
                style={{ width: selectWidth }}
                placeholder="Select task type"
                options={options}
                value={value}
                onChange={onChange}
            />
            <Button 
                type="dashed" 
                icon={<PlusOutlined />}
                onClick={onAddTask}
                disabled={!value}
            >
                Add Task
            </Button>
        </Space>
    );
};

// Export default task types that can be used across the application
export const defaultTaskTypes: TaskTypeOption[] = [
    { label: 'Dummy Task', value: 'dummy' },
    { label: 'Conditional Task', value: 'conditional' }
];

export default TaskTypeSelect;

