"use client"

import React from 'react';
import { Table, Popover } from 'antd';
import type { TableColumnsType } from 'antd';
import { DownOutlined, SmileOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';

interface DataType {
    key: React.Key;
    name: string;
    age: number;
    address: string;
    description: string;
}

const columns: TableColumnsType<DataType> = [
    { title: 'Name', dataIndex: 'name', key: 'name' },
    { title: 'Age', dataIndex: 'age', key: 'age' },
    Table.SELECTION_COLUMN,
    { title: 'Address', dataIndex: 'address', key: 'address' },
];

const data: DataType[] = [
    {
        key: 1,
        name: '<PERSON>',
        age: 32,
        address: 'New York No. 1 Lake Park',
        description: 'My name is <PERSON>, I am 32 years old, living in New York No. 1 Lake Park.',
    },
    {
        key: 2,
        name: '<PERSON>',
        age: 42,
        address: 'London No. 1 Lake Park',
        description: 'My name is <PERSON>, I am 42 years old, living in London No. 1 Lake Park.',
    },
    {
        key: 3,
        name: 'Not Expandable',
        age: 29,
        address: 'Jiangsu No. 1 Lake Park',
        description: 'This not expandable',
    },
    {
        key: 4,
        name: '<PERSON>',
        age: 32,
        address: 'Sydney No. 1 Lake Park',
        description: 'My name is Joe Black, I am 32 years old, living in Sydney No. 1 Lake Park.',
    },
];
const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
          1st menu item
        </a>
      ),
    },
    {
      key: '2',
      label: (
        <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
          2nd menu item (disabled)
        </a>
      ),
      icon: <SmileOutlined />,
      disabled: true,
    },
    {
      key: '3',
      label: (
        <a target="_blank" rel="noopener noreferrer" href="https://www.luohanacademy.com">
          3rd menu item (disabled)
        </a>
      ),
      disabled: true,
    },
    {
      key: '4',
      danger: true,
      label: 'a danger item',
    },
  ];

const App: React.FC = () => {

    const [hoverRow, SetHoverRow] = React.useState();

    const isHoveringAnother = (row) => {
        //if(hoverRow == row) SetHoverRow(null);
    }

    return (
        <Table<DataType>
            columns={columns}
            onRow={(record, rowIndex) => {
                return {
                    onClick: (event) => { }, // click row
                    onDoubleClick: (event) => { }, // double click row
                    onContextMenu: (event) => { }, // right button click row
                    onMouseEnter: (event) => { SetHoverRow(record.key); }, // mouse enter row
                    onMouseLeave: (event) => { setTimeout(isHoveringAnother(record.key), 100); }, // mouse leave row
                };
            }}
            
            rowSelection={{}}
            expandable={{
                expandedRowRender: (record) => <p style={{ margin: 0 }}>{record.description}</p>,
                expandedRowKeys: [hoverRow],
                showExpandColumn: false,
            }}
            dataSource={data}
        />)
};

export default App;