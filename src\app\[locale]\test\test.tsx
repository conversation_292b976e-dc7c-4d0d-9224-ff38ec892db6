'use client'

import { QueryBuilderDnD } from '@react-querybuilder/dnd';
import * as ReactDnD from 'react-dnd';
import * as ReactDndHtml5Backend from 'react-dnd-html5-backend';
import { QueryBuilderAntD } from '@react-querybuilder/antd';
import { useState } from 'react';
import { type Field, OptionGroup, QueryBuilder, type RuleGroupType } from 'react-querybuilder';
import '@ant-design/v5-patch-for-react-19';
import "./qb.css";


const fields: Field[] = [
    { name: 'firstName', label: 'First Name' },
    { name: 'lastName', label: 'Last Name' },
];

const fields2: OptionGroup<Field>[] = [
    {
        label: 'Variables', options: [
            { name: 'firstName', label: 'First Name' },
            { name: 'lastName', label: 'Last Name' },
        ]
    }
];

export default function AppTest() {
    const [query, setQuery] = useState<RuleGroupType>({ combinator: 'and', rules: [] });

    return (
        <QueryBuilderDnD dnd={{ ...ReactDnD, ...ReactDndHtml5Backend }}>
            <QueryBuilderAntD>
                <QueryBuilder fields={fields2} defaultQuery={query} onQueryChange={setQuery} />
            </QueryBuilderAntD>
        </QueryBuilderDnD>
    );
}