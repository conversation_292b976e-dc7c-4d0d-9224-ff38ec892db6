import { But<PERSON>, Di<PERSON>r, <PERSON>, Typography } from "antd";
import { BaseNodeFormElement } from "../base-node";
import { LoopNodeModelData } from "./loop-node-model";
import React from "react";
import Condition from "@/components/flowbuilder/conditions/condition";
import { RuleGroupType } from "react-querybuilder";

export interface LoopNodeFormProps extends BaseNodeFormElement<LoopNodeModelData> {
}

export default function LoopNodeForm(props: LoopNodeFormProps) {
    const [condition, setCondition] = React.useState<RuleGroupType>(
        props.data.condition || { combinator: 'and', rules: [] }
    );

    const handleSave = () => {
        props.onChange({
            condition: condition
        });
        props.onCancel();
    };

    // For now, using empty fields array. In a real implementation, 
    // this would be passed from the parent component
    const fields = [];

    return (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Typography.Title level={4}>Loop Condition</Typography.Title>
            <Typography.Text type="secondary">
                Define the condition that must be true for the loop to continue executing.
            </Typography.Text>
            
            <Condition 
                fields={fields} 
                query={condition} 
                onChange={setCondition}
                onCancel={() => {}}
            />
            
            <Divider />
            <Space>
                <Button type="primary" onClick={handleSave}>
                    Save
                </Button>
                <Button onClick={props.onCancel}>
                    Cancel
                </Button>
            </Space>
        </Space>
    );
}
