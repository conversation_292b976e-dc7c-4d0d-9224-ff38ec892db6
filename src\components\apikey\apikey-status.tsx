import { Api<PERSON><PERSON> } from "@/models/apikey";
import { Tag } from "antd";
import {
    ExclamationCircleOutlined,
    PauseCircleOutlined,
    CheckCircleOutlined
} from '@ant-design/icons';

interface ApiKeyStatusParams {
    apiKey: ApiKey;
}

const ApiKeyStatus: React.FC<ApiKeyStatusParams> = (params) => {
    if (params.apiKey.expireAt < Math.floor(Date.now() / 1000)) {
        return (<Tag icon={<ExclamationCircleOutlined />} color="red">expired</Tag>)
    } else if (params.apiKey.paused) {
        return (<Tag icon={<PauseCircleOutlined />} color="red">paused</Tag>)
    } else {
        return (<Tag icon={<CheckCircleOutlined />} color="success">active</Tag>)
    }

}

export default ApiKeyStatus;