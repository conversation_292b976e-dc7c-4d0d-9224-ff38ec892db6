'use client'

import { <PERSON><PERSON><PERSON><PERSON>, MCPServerController } from "@/models/mcpserver";
import <PERSON>PServerList from "./list/mcpserver-list";
import { <PERSON><PERSON>, Divider, Drawer, Space, Typography } from "antd";
import ConfirmDeleteModal from "@/components/confirm-delete-modal";
import React from "react";
import {
    PlusOutlined,
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import MCPServerPutDrawer from "./drawers/put-drawer";
import MCPServerViewDrawer from "./drawers/view-drawer";
import MCPServerDeployDrawer from "./drawers/deploy-drawer";

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions {
    mcpServer?: MCPServer;
    open: boolean;
}

const MCPServersComponent: React.FC = () => {
    const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
    const [mutateObject, setMutateObject] = React.useState<MCPServer[]>([]);
    const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({ open: false });

    const onCloseDrawer = () => {
        setDrawerOptions(emptyDrawer);
    };

    const onCreate = () => {
        setDrawerOptions({ 
            title: "Create MCP Server", 
            isOpen: true, 
            component: <MCPServerPutDrawer isOpen={true} onSuccess={onSuccess} mode='create' /> 
        })
    }

    const onView = (record: MCPServer) => {
        setDrawerOptions({ 
            title: `MCP Server Details - ${record.name}`, 
            isOpen: true, 
            component: <MCPServerViewDrawer mcpServer={record} isOpen={true} onClose={onCloseDrawer} /> 
        })
    }

    const onEdit = (record: MCPServer) => {
        setDrawerOptions({ 
            title: "Edit MCP Server", 
            isOpen: true, 
            component: <MCPServerPutDrawer isOpen={true} onSuccess={onSuccess} mode='edit' mcpServer={record} /> 
        })
    }

    const onDeploy = (record: MCPServer) => {
        setDrawerOptions({ 
            title: `Deploy MCP Server - ${record.name}`, 
            isOpen: true, 
            component: <MCPServerDeployDrawer mcpServer={record} isOpen={true} onSuccess={onSuccess} onClose={onCloseDrawer} /> 
        })
    }

    const onSuccess = (record: MCPServer) => {
        setMutateObject([record]);
        onCloseDrawer();
    }

    const onDelete = (record: MCPServer) => {
        setDeleteModalOptions({ open: true, mcpServer: record })
    }

    const onDeleteConfirm = (record: MCPServer) => {
        setDeleteModalOptions({ open: false });
        setMutateObject([record]);
    }

    const onDeleteCancel = () => {
        setDeleteModalOptions({ open: false });
    }

    return (
        <>
            <ConfirmDeleteModal<MCPServer>
                controller={new MCPServerController()}
                open={deleteModalOptions.open}
                objectToDelete={deleteModalOptions.mcpServer}
                onDelete={onDeleteConfirm}
                onCancel={onDeleteCancel}
            />
            
            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                <Space direction="horizontal" size="middle" style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography.Title level={2}>
                        MCP Servers
                    </Typography.Title>
                    <Button size='large' type='primary' onClick={onCreate}>
                        <PlusOutlined />Create MCP Server
                    </Button>
                </Space>
                <Divider />
                <MCPServerList
                    onClick={onView}
                    onDelete={onDelete}
                    onEdit={onEdit}
                    onDeploy={onDeploy}
                    mutateObjects={mutateObject}
                />
            </Space>

            <Drawer
                title={drawerOptions.title}
                placement="right"
                size="large"
                onClose={onCloseDrawer}
                open={drawerOptions.isOpen}
                extra={
                    <Space>
                        <Button onClick={onCloseDrawer}>Cancel</Button>
                    </Space>
                }
            >
                {drawerOptions.component}
            </Drawer>
        </>
    )
}

export default MCPServersComponent;
