'use client'

import { <PERSON>, Agent<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, AgentStatus } from "@/models/agent";
import { Button, Col, Divider, Flex, Form, Input, Result, Row, Skeleton, Space, Spin, Steps } from "antd";
import React, { useEffect } from "react";
import {
    LoadingOutlined,
    CheckCircleOutlined,
    CheckCircleFilled
} from '@ant-design/icons';
import SubmitButton from "@/components/submit-button";
import Title from "antd/es/typography/Title";
import { SWRConfiguration } from "swr";
import FetcherErrorDisplay from "@/components/fetcher-error-display";
import DrawerTemplate from "@/templates/drawer-template";


interface AgentDeployDrawerParams {
    agentId: string;
}

interface AgentDeployerProps {
    agentId: string;
    onLoadingChange: (isLoading: boolean) => void;
    onDataChange: (data: Agent) => void;
}

const getStepFromStatus = (status: AgentStatus) => {
    switch (status) {
        case AgentStatus.SEALING:
            return 0;
        case AgentStatus.PREPARING:
            return 1;
        case AgentStatus.VERSIONING:
        case AgentStatus.WAITING_VERSION:
            return 2;
        case AgentStatus.TAGGING:
            return 3;
        default:
            return -1;
    }
}
const steps = [
    {
        title: 'Sealing',
        description: 'Locking your Agent'
    },
    {
        title: 'Preparing',
        description: 'Preparing the new agent version'
    },
    {
        title: 'Versioning',
        description: 'Assigning a version to your agent'
    },
    {
        title: 'Tagging',
        description: 'Wrapping up by tagging the agent'
    }
]


const AgentDeployer: React.FC<AgentDeployerProps> = (params) => {
    const agentController: AgentController = new AgentController()

    const [getOptions, setGetOptions] = React.useState<SWRConfiguration>({ revalidateIfStale: false, refreshInterval: 0 });
    const { data, error, isLoading, mutate, isValidating } = agentController.useGet([params.agentId], getOptions);

    const [form] = Form.useForm();

    const { data: deployData, error: deployError, trigger, isMutating } = agentController.useDeploy(data ? data[0].agentId : '')

    React.useEffect(() => {
        if (!data || !deployData) return;
        mutate(async (data) => {
            if (!data || !deployData) return data;
            data[0] = deployData
            setTimeout(() => setGetOptions({ revalidateIfStale: true, refreshInterval: 5000 }), 5000);
        }, { revalidate: false })
    }, [deployData])

    useEffect(() => {
        if (data) params.onDataChange(data[0])
        if (data && getStepFromStatus(data[0].status) != -1) {
            setGetOptions({ revalidateIfStale: true, refreshInterval: 5000 })
        }
    }, [data])
    useEffect(() => params.onLoadingChange(isLoading), [isLoading])

    const buildSteps = (step: number) => {
        return steps.map(
            (value, index) => {
                if (index == step) return {
                    title: <Space><LoadingOutlined />{value.title}</Space>,
                    description: value.description
                }
                else if (index < step) return {
                    title: <Space>{value.title}</Space>,
                    description: value.description,
                }
                else return value;
            }
        )
    }

    if (error || deployError) {
        return <FetcherErrorDisplay error={error ?? deployError} type="page" />
    }

    if (data === undefined) {
        return <Skeleton />
    }

    if (agentController.can(AgentActions.DEPLOY, data[0])) {
        return (
            <>
                <Spin spinning={isLoading || isValidating || isMutating}>
                    <Title level={5}>Deploy a new Tag</Title>
                    <Form form={form} layout="vertical" autoComplete="off" onFinish={() => {
                        if (data[0].status === AgentStatus.READY)
                            trigger({ body: { tag: form.getFieldValue('tag'), description: form.getFieldValue('description') } })
                    }}>
                        <Form.Item name="name" label="Tag" rules={[{ required: true }, { min: 3 }]}>
                            <Input />
                        </Form.Item>
                        <Form.Item
                            name="description"
                            label="Description"
                            rules={[{ required: true }, { min: 10 }, { max: 250, message: 'Please input a description of up to 250 characters' }]}
                            required
                        >
                            <Input.TextArea rows={4} count={{
                                show: true,
                                max: 250,
                            }} />
                        </Form.Item>
                        <Form.Item>
                            <Space>
                                <SubmitButton form={form}>Deploy</SubmitButton>
                            </Space>
                        </Form.Item>
                    </Form>
                </Spin>
            </>
        )
    }


    const step = getStepFromStatus(data[0].status)
    if (step == -1) {
        return <Result
            status="error"
            title="You can't deploy in the current state"
            subTitle="Your agent is in an state that doesn't allow deploying, please wait until it is in READY state."
        />;
    }

    return (
        <>
            <Title level={5}>Deployment in progress</Title>
            <Steps
                direction="vertical"
                progressDot
                current={step}
                items={buildSteps(step)}
            />
        </>
    )



}

const AgentDeployDrawer: React.FC<AgentDeployDrawerParams> = (params) => {

    const [data, setData] = React.useState<Agent>()
    const [loading, setLoading] = React.useState<boolean>(false);


    return <DrawerTemplate
        title={(data) ? 'Deploy ' + data.name : 'Loading...'}
        isLoading={loading}
    >
        <AgentDeployer
            agentId={params.agentId}
            onLoadingChange={(isLoading: boolean) => { setLoading(isLoading) }}
            onDataChange={(knowledge: Agent) => { setData(knowledge) }}
        />
    </DrawerTemplate>

}

export default AgentDeployDrawer;