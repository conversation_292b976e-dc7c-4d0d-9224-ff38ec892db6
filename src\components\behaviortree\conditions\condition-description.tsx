import React from "react";
import { defaultOperators, Field, formatQuery, OptionGroup, RuleGroupTypeAny } from "react-querybuilder";

interface ConditionDescriptionProps {
    contitionData ? : RuleGroupTypeAny;
    fields?: OptionGroup<Field>[];
}

export default function ConditionDescription(props: ConditionDescriptionProps) {
    if(props.fields === undefined || props.fields.length == 0 || props.contitionData === undefined)
        return ('No rules.')

    return (
        formatQuery(props.contitionData, {
            format: 'natural_language',
            parseNumbers: true,
            getOperators: () => defaultOperators,
            fields: props.fields,
        })
    );
}