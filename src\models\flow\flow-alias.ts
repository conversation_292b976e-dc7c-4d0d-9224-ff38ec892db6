import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "../base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export interface FlowAlias extends IBasemodel {
    id: string;
    flowId: string;
    alias: string;
    description: string;
    flowTagId: string;
    flowName?: string;
    tagName?: string;
}

export enum FlowAliasActions {
    EDIT,
    DELETE,
    CREATE,
    SWAP
}

export interface FlowAliasCreateRequest {
    flowId: string;
    alias: string;
    description: string;
    flowTagId: string;
}

export interface FlowAliasUpdateRequest {
    id: string;
    alias: string;
    description: string;
    flowTagId: string;
}

export interface FlowAliasSwapRequest {
    id: string;
    newFlowTagId: string;
}

export class FlowAliasController implements IBasemodelController<FlowAlias, FlowAliasActions> {
    getId: (item: FlowAlias) => string = (item) => item.id;
    
    useGet = (ids: string[], options?: SWRConfiguration) => 
        Fetcher2.SWRMultiTemplate<FlowAlias>(
            ids.map((id) => APIRoutes.FlowAliasController.GET.replace("{id}", id)), 
            { method: 'GET' }, 
            options
        )
    
    can = (action: FlowAliasActions, onItem: FlowAlias) => {
        return true;
    }
    
    useList = (request: ListRequest) => 
        Fetcher2.SWRTemplate<ListResponse<FlowAlias>>(
            APIRoutes.FlowAliasController.LIST, 
            {method: 'GET', queryString: request}
        )

    useListByFlow = (flowId: string) => 
        Fetcher2.SWRTemplate<ListResponse<FlowAlias>>(
            APIRoutes.FlowAliasController.LIST_BY_FLOW, 
            {method: 'GET', urlPlaceholders: {flowId: flowId}}
        )
    
    useDelete = (id: string) => 
        Fetcher2.SWRMutationTemplate<FlowAlias>(
            APIRoutes.FlowAliasController.DELETE, 
            {method: 'DELETE', urlPlaceholders: {id: id}}
        )

    useCreate = () => 
        Fetcher2.SWRMutationTemplate<FlowAlias>(
            APIRoutes.FlowAliasController.CREATE, 
            {method: 'POST'}
        )

    useUpdate = () => 
        Fetcher2.SWRMutationTemplate<FlowAlias>(
            APIRoutes.FlowAliasController.EDIT, 
            {method: 'PUT'}
        )

    useSwap = (id: string) => 
        Fetcher2.SWRMutationTemplate<FlowAlias>(
            APIRoutes.FlowAliasController.SWAP, 
            {method: 'PUT', urlPlaceholders: {id: id}}
        )

    // Check if a tag is used by any alias
    useCheckTagUsage = (tagId: string) => 
        Fetcher2.SWRTemplate<{isUsed: boolean, aliases: FlowAlias[]}>(
            APIRoutes.FlowAliasController.CHECK_TAG_USAGE, 
            {method: 'GET', urlPlaceholders: {tagId: tagId}}
        )
}
