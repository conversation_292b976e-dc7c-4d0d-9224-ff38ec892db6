import { Flow, FlowController } from "@/models/flow";
import { Button, Dropdown, Space } from "antd";
import type { TableColumnsType } from 'antd';
import React from "react";
import {
    EllipsisOutlined,
    EyeOutlined,
    EditOutlined,
    DeleteOutlined,
    BuildOutlined,
    TagsOutlined,
    SignatureOutlined,
} from '@ant-design/icons';
import DataTable from "@/components/data-table";
import { useTimeAgo } from "next-timeago";

interface FlowListProps {
    mutateObjects?: Flow[];
    onClick?: (record: Flow) => void;
    onEdit?: (record: Flow) => void;
    onDelete?: (record: Flow) => void;
    onFlowBuilder?: (record: Flow) => void;
    onCreateTag?: (record: Flow) => void;
    onCreateAlias?: (record: Flow) => void;
}

const FlowList: React.FC<FlowListProps> = (params) => {
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState<boolean>(false);

    const contextMenuProps = (record: Flow) => ({
        items: [
            {
                key: 'view',
                label: 'View Details',
                icon: <EyeOutlined />,
                onClick: () => {
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }
            },
            {
                key: 'edit',
                label: 'Edit',
                icon: <EditOutlined />,
                onClick: () => {
                    if (params.onEdit) {
                        params.onEdit(record);
                    }
                }
            },
            {
                key: 'flowbuilder',
                label: 'Flow Builder',
                icon: <BuildOutlined />,
                onClick: () => {
                    if (params.onFlowBuilder) {
                        params.onFlowBuilder(record);
                    }
                }
            },
            {
                type: 'divider'
            },
            {
                key: 'create-tag',
                label: 'Create Tag',
                icon: <TagsOutlined />,
                onClick: () => {
                    if (params.onCreateTag) {
                        params.onCreateTag(record);
                    }
                }
            },
            {
                key: 'create-alias',
                label: 'Create Alias',
                icon: <SignatureOutlined />,
                onClick: () => {
                    if (params.onCreateAlias) {
                        params.onCreateAlias(record);
                    }
                }
            },
            {
                type: 'divider'
            },
            {
                key: 'delete',
                label: 'Delete',
                icon: <DeleteOutlined />,
                danger: true,
                onClick: () => {
                    if (params.onDelete) {
                        params.onDelete(record);
                    }
                }
            }
        ]
    });

    const columns: TableColumnsType<Flow> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Name', 
            dataIndex: 'name', 
            render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{value}</a>;
            }
        },
        { 
            title: 'Description', 
            dataIndex: 'description',
            ellipsis: true
        },
        {
            title: 'Current Tag', 
            dataIndex: 'currentTagId',
            render: (value) => value || 'No tag set'
        },
        {
            title: 'Created At', 
            dataIndex: 'createdAt', 
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        },
        {
            title: 'Last Modified', 
            dataIndex: 'lastChangeTimestamp', 
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<Flow, FlowController>
            controller={new FlowController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: Flow) => false}
            mutateItems={params.mutateObjects}
            rowKey="id"
        />
    )
}

export default FlowList;
