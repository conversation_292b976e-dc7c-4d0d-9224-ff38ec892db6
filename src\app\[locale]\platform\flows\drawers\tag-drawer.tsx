import { Flow } from "@/models/flow";
import { FlowTag, FlowTagController, FlowTagCreateRequest, FlowTagUpdateRequest } from "@/models/flow/flow-tag";
import { Button, Form, Input, Space, message, Switch, Alert } from "antd";
import React from "react";

interface FlowTagDrawerProps {
    isOpen: boolean;
    onSuccess: (flowTag: FlowTag) => void;
    mode: 'create' | 'edit';
    flow?: Flow;
    flowTag?: FlowTag;
}

const FlowTagDrawer: React.FC<FlowTagDrawerProps> = ({ isOpen, onSuccess, mode, flow, flowTag }) => {
    const [form] = Form.useForm();
    const flowTagController = new FlowTagController();
    const { trigger: createTrigger, isMutating: isCreating } = flowTagController.useCreate();
    const { trigger: updateTrigger, isMutating: isUpdating } = flowTagController.useUpdate();

    React.useEffect(() => {
        if (isOpen) {
            if (mode === 'edit' && flowTag) {
                form.setFieldsValue({
                    tagName: flowTag.tagName,
                    flowDefinition: flowTag.flowDefinition,
                    setAsLatest: flowTag.isLatest
                });
            } else {
                form.resetFields();
                form.setFieldsValue({
                    setAsLatest: true // Default to true for new tags
                });
            }
        }
    }, [isOpen, mode, flowTag, form]);

    const handleSubmit = async (values: any) => {
        try {
            let result;
            if (mode === 'create' && flow) {
                const request: FlowTagCreateRequest = {
                    flowId: flow.id,
                    tagName: values.tagName,
                    flowDefinition: values.flowDefinition,
                    setAsLatest: values.setAsLatest
                };
                result = await createTrigger(request);
            } else if (mode === 'edit' && flowTag) {
                const request: FlowTagUpdateRequest = {
                    id: flowTag.id,
                    tagName: values.tagName,
                    flowDefinition: values.flowDefinition
                };
                result = await updateTrigger(request);
            }

            if (result) {
                message.success(`Tag ${mode === 'create' ? 'created' : 'updated'} successfully`);
                onSuccess(result);
            }
        } catch (error) {
            message.error(`Failed to ${mode} tag`);
        }
    };

    const isLoading = isCreating || isUpdating;

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            disabled={isLoading}
        >
            {mode === 'create' && flow && (
                <Alert
                    message={`Creating tag for flow: ${flow.name}`}
                    type="info"
                    style={{ marginBottom: 16 }}
                />
            )}

            <Form.Item
                name="tagName"
                label="Tag Name"
                rules={[
                    { required: true, message: 'Please enter a tag name' },
                    { min: 2, message: 'Tag name must be at least 2 characters' }
                ]}
            >
                <Input placeholder="Enter tag name (e.g., v1.0, production, staging)" />
            </Form.Item>

            <Form.Item
                name="flowDefinition"
                label="Flow Definition"
                rules={[
                    { required: true, message: 'Please enter the flow definition' }
                ]}
            >
                <Input.TextArea 
                    rows={8} 
                    placeholder="Enter flow definition JSON"
                />
            </Form.Item>

            {mode === 'create' && (
                <Form.Item
                    name="setAsLatest"
                    label="Set as Latest"
                    valuePropName="checked"
                >
                    <Switch />
                </Form.Item>
            )}

            <Form.Item>
                <Space>
                    <Button 
                        type="primary" 
                        htmlType="submit" 
                        loading={isLoading}
                    >
                        {mode === 'create' ? 'Create' : 'Update'} Tag
                    </Button>
                </Space>
            </Form.Item>
        </Form>
    );
};

export default FlowTagDrawer;
