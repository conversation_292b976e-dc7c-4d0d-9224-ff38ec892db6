import { NodeProps, Node } from '@xyflow/react';
import { RuleGroupType } from 'react-querybuilder';

export interface NodeEvents<T>{
  onOpenDrawer : () => void;
}

export type BaseNodeData<T> = {
  name: string;
  nodeData: T;
  nodeConditions? : RuleGroupType;
}

export type BaseNode<T> = Node<{ baseData : BaseNodeData<T>, events?: NodeEvents<T>}>

export interface BaseNodeFormElement<T>{
  data : T;
  onChange : (data : T) => void;
  onCancel : () => void;
}


export type BaseNodeProps<T> = NodeProps<BaseNode<T>>