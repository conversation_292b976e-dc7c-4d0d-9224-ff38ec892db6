import { BaseNode } from '../base-node';
import { RuleGroupType } from 'react-querybuilder';

export interface SwitchCondition {
    id: string;
    condition: RuleGroupType;
    label?: string;
}

export type SwitchNodeModelData = {
    conditions: SwitchCondition[];
}

export type SwitchNodeModel = BaseNode<SwitchNodeModelData>;

export function InitializeSwitchNodeModelData(): SwitchNodeModelData {
    return {
        conditions: []
    }
}
