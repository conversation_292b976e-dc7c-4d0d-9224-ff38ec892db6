'use client'

import '@ant-design/v5-patch-for-react-19';
import React, { useEffect } from 'react';
import { Button, Table, Empty, theme, Divider, Tag, Space, Dropdown, Spin } from 'antd';
import type { TableColumnsType, TableProps, MenuProps } from 'antd';
import {
    ReloadOutlined,
    ExclamationCircleOutlined,
    EditOutlined,
    RocketOutlined,
    DeleteOutlined,
    EllipsisOutlined,
    CloudUploadOutlined,
    LoadingOutlined
} from '@ant-design/icons';
import { getKnowledgebase, KnowledgeBase, listKnowledgebases } from '@/models/knowledgebase';
import { useTimeAgo } from 'next-timeago';
import { GetKnowledgebaseStatusTag } from '@/components/knowledgebase/knowledgebase-description';

export interface APIData {
    kbId: React.Key;
    name: string;
    description: string;
    status: KnowledgeBaseStatus;
    tag: string;
}

export interface ListKnowledgeBaseRequest {
    count: number;
    nextToken?: string;
}

export interface ListKnowledgeBaseResponse {
    entries: KnowledgeBase[];
    nextToken?: string;
    total: number;
}

enum KnowledgeBaseStatus {
    QUEUED = "QUEUED",
    DB_ENTRY_CREATED = "DB_ENTRY_CREATED",
    KB_CREATED = "KB_CREATED",
    DATASOURCE_CREATED = "DATASOURCE_CREATED",
    READY = "READY",
    SEALING = "SEALING",
    DEPLOYING = "DEPLOYING",
    SYNCRONIZING = "SYNCRONIZING",
    TAGGING = "TAGGING",
    DISABLED = "DISABLED",
}



const handleMenuClick: MenuProps['onClick'] = (e) => {
    console.log('click', e);
};





export interface ListKnowledgeBasesComponentProps {
    onClick?: ((record: KnowledgeBase) => void) | null;
    onEdit?: ((record: KnowledgeBase) => void) | null;
    onDeploy?: ((record: KnowledgeBase) => void) | null;
    onDelete?: ((record: KnowledgeBase) => void) | null;
    onUploadData?: ((record: KnowledgeBase) => void) | null;
    //mutateObject?: KnowledgeBase;
    mutateItems?: KnowledgeBase[];

}

const ListKnowledgeBasesComponent: React.FC<any> = (props: ListKnowledgeBasesComponentProps) => {
    const [kbQueryParams, setKbQueryParams] = React.useState<ListKnowledgeBaseRequest>({ count: -1 });
    const { data, error, isLoading, mutate } = listKnowledgebases(kbQueryParams);
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const { data: statusData, isValidating: statusIsLoading } = getKnowledgebase(pendingData, { refreshInterval: 10000, revalidateIfStale: true });

    const {
        token: { colorError },
    } = theme.useToken();
    const { TimeAgo } = useTimeAgo();

    const items = (record : KnowledgeBase) : MenuProps['items'] => ([
        {
            label: 'Edit',
            key: '1',
            icon: <EditOutlined />,
            onClick: () => { if(props.onEdit) props.onEdit(record); }
        },
        {
            label: 'Upload Data',
            key: '4',
            icon: <CloudUploadOutlined />,
            onClick: () => { if(props.onUploadData) props.onUploadData(record); }
        },
        {
            label: 'Deploy',
            key: '2',
            icon: <RocketOutlined />,
            onClick: () => { if(props.onDeploy) props.onDeploy(record); },
        },
        {
            label: 'Delete',
            key: '3',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => { if(props.onDelete) props.onDelete(record); },
        },
    ]);

    const menuProps = (record:KnowledgeBase) => ({
        items: items(record),
        onClick: handleMenuClick,
    });


    /*useEffect(() => {
        if (props.mutateObject === undefined || props.mutateObject == null) return;
        mutate(async (data: ListKnowledgeBaseResponse | undefined) => {
            if (data === undefined || props.mutateObject === undefined || props.mutateObject == null) return data;
            const filteredData = data.entries.filter((item: KnowledgeBase) => item.kbId !== props.mutateObject?.kbId)
            filteredData.push(props.mutateObject);
            return { ...data, knowledgeBases: filteredData };
        }, { revalidate: false })
    }, [props.mutateObject])*/


    useEffect(() => {
        console.log("Got mutating items from parent: ", props.mutateItems)
        if (props.mutateItems === undefined || props.mutateItems.length == 0) return;
        mutate(async (data: ListKnowledgeBaseResponse | undefined) => {
            if (data === undefined || props.mutateItems === undefined || props.mutateItems.length == 0) return data;

            const mutatedIds = props.mutateItems.map((item: KnowledgeBase) => item.kbId)
            const filteredData = data.entries.filter((item: KnowledgeBase) => !mutatedIds.includes(item.kbId))
            filteredData.push(...props.mutateItems);

            return { ...data, knowledgeBases: filteredData };
        }, { revalidate: false })
    }, [props.mutateItems])


    useEffect(() => {
        const pendingDataUpdate = data?.entries.filter((value: KnowledgeBase) => value.status != KnowledgeBaseStatus.READY).map((value: KnowledgeBase) => value.kbId) ?? [];

        //removes statuses that were made ready
        setPendingData(pendingData.filter((key: string) => pendingDataUpdate.includes(key)));

        const interval = setTimeout(() => {
            setPendingData(pendingDataUpdate);
        }, 10000);
        return () => clearTimeout(interval);
    }, [data])

    useEffect(() => {
        const pendingDataUpdate = statusData?.filter((value: KnowledgeBase) => value.status != KnowledgeBaseStatus.READY).map((value: KnowledgeBase) => value.kbId) ?? [];
        const updatedKbs = statusData?.filter((value: KnowledgeBase) => value.status == KnowledgeBaseStatus.READY) ?? [];
        const updatedKbsIds = updatedKbs.map((item: KnowledgeBase) => item.kbId);

        if (updatedKbs.length > 0 && (statusData ?? []).length > 0) {
            mutate(async (data: ListKnowledgeBaseResponse | undefined) => {
                if (data === undefined) return data;

                const dataMinusUpdates = data.entries.filter((item: KnowledgeBase) => !updatedKbsIds.includes(item.kbId))
                dataMinusUpdates.push(...updatedKbs);

                return { ...data, knowledgeBases: dataMinusUpdates };

            }, { revalidate: false })
        }


        if (pendingDataUpdate.length > 0 && (statusData ?? []).filter((arr1Item: KnowledgeBase) => !pendingDataUpdate.includes(arr1Item.kbId)).length > 0) {
            const interval = setInterval(() => {
                if (pendingDataUpdate.length > 0) {
                    setPendingData(pendingDataUpdate);
                }
            }, 10000);
            return () => clearInterval(interval);
        }
    }, [statusData])



    const columns: TableColumnsType<KnowledgeBase> = [
        {
            title: (<Button type="text" onClick={() => { mutate(); }} danger>
                <ReloadOutlined />
            </Button>), render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={menuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Name', dataIndex: 'name', render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (props.onClick != null) {
                        console.log(record);
                        props.onClick(record);
                    }
                }}>{value}</a>;
            }
        },
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'Status', dataIndex: 'status', render: (status: KnowledgeBaseStatus, record: KnowledgeBase, index) => {
                const text = GetKnowledgebaseStatusTag(status);
                return (
                    <Space size={'small'}>
                        {text}
                        <Spin key={record.kbId} indicator={<LoadingOutlined spin />} spinning={pendingData.includes(record.kbId) && statusIsLoading} />
                    </Space>
                );
            },
            shouldCellUpdate: (record: KnowledgeBase, prevRecord: KnowledgeBase) => {
                if (record === undefined || prevRecord === undefined) return true;
                return (record.status != KnowledgeBaseStatus.READY || (pendingData.includes(record.kbId) && statusIsLoading) || record.status != prevRecord.status)
            },
            width: 160,
        },
        { title: 'Tag', dataIndex: 'tag' },
        {
            title: 'Last Modified', dataIndex: 'lastChangeTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    const nodataReason = error ? (<><Divider />
        <p style={{ color: colorError }} ><ExclamationCircleOutlined /> Couldn't load your data, please check your internet connection.</p>
        <Button onClick={() => { mutate() }} danger={true}><ReloadOutlined /></Button></>) : <p>You can create a new Knowledgebase clicking here.</p>


    return (
        <>
            <Table<KnowledgeBase>
                rowSelection={{ type: 'checkbox' }}
                loading={isLoading}
                columns={columns}
                dataSource={data?.entries}
                rowKey='kbId'
                pagination={{ position: ['bottomCenter'] }}
                locale={{ emptyText: <Empty description="No Data">{nodataReason}</Empty> }}
            />
        </>
    )
}

export default ListKnowledgeBasesComponent;