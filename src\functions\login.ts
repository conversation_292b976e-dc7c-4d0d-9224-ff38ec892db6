import Cookies from 'universal-cookie';
import { jwtDecode } from 'jwt-decode';
import type { JwtPayload} from 'jwt-decode';
import { Fetcher } from '@/functions/fetcher';
import { APIRoutes, Routes } from '@/constants';
import { useRouter } from 'next/navigation';

export namespace LoginFunctions {
    enum SigninStatus {
        CREDENTIALS_NOT_FOUND = "CREDENTIALS_NOT_FOUND",
        EMAIL_NOT_CONFIRMED = "EMAIL_NOT_CONFIRMED",
        NEW_PASSWORD_REQUIRED = "NEW_PASSWORD_REQUIRED",
        MFA_REQUIRED = "MFA_REQUIRED",
        LOGIN_SUCCESSFUL = "LOGIN_SUCCESSFUL",
        INTERNAL_ERROR = "INTERNAL_ERROR"
    }
    
    interface SigninResponse {
        sessionId: string;
        status?: SigninStatus;
        userId?: string;
        accountId?: string;
        accessToken?: string;
        refreshToken?: string;
        jwt?: string;
        RefreshTimeout: number;
    }
    
    interface SigninRequest {
        email: string;
        password: string;
    }
    
    export interface RequestLoginResponse {
        success : boolean;
        errorTitle? : string;
        errorMessage? : string;
    }

    interface UpdateJwtResponse{
        jwt? : string;
    }
    
    export async function RequestLogin(email : string, password : string, rememberMe : boolean) : Promise<RequestLoginResponse>{
        let request : SigninRequest = {email: email, password: password}
        return fetch('http://localhost:5083/signin', {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(request)
          })
          .then(function(res){ return res.json(); })
          .then(function(data : SigninResponse){
            switch(data.status){
                case SigninStatus.CREDENTIALS_NOT_FOUND:
                    return {success: false, errorTitle: 'User not found', errorMessage: 'Could not find your credentials, please verify and try again.' };
                case SigninStatus.LOGIN_SUCCESSFUL:
                    console.log(data);
                    HandleLogin(rememberMe, data);
                    return {success: true };
                default:
                    return {success: false, errorTitle:'Something is not right...', errorMessage:'Unhandled status, please message us to report this issue.' };
            }
        }).catch(({ name, message }) => {
            return {success: false, errorTitle:'Something is not right...', errorMessage:message };
          });
      };
    
    
    function HandleLogin(rememberMe : Boolean, loginResults : SigninResponse) : boolean{
        let cookies = new Cookies(null, { path: '/' });
    
        if(loginResults.status == SigninStatus.LOGIN_SUCCESSFUL){
            if(rememberMe){
                cookies.set("loginRefreshToken", loginResults.refreshToken);
            }
            if(loginResults.jwt != null) {
                window.localStorage.setItem("jwt:value", loginResults.jwt);
                return true;
            }
            return false;
        }
        return false;
    }

    export function Logout(){
        window.localStorage.removeItem("jwt:value");
        window.location.href = '/login';
        return true;
    }
    
    export function RefreshLoginJwt(){
        let jwt : string | null = window.localStorage.getItem("jwt:value");

        if(jwt == null) return;

        let decoded_token : JwtPayload | null = null;

        try{
            decoded_token = jwtDecode(jwt);
        }catch(error){
            return;
        }

        if(decoded_token == null) return;

        let ts = Math.floor(Date.now() / 1000);
        let expireAt = decoded_token.exp ?? ts;
        let nextRefresh = 1000*(expireAt - ts)/2 - 5000;

        if(nextRefresh <= 0) {
            return;
        }
        
        setTimeout(() => {
            Fetcher
            .sfetch<UpdateJwtResponse>(APIRoutes.GetURL(APIRoutes.SignInController.UPDATE), {method: 'POST', auth: true, timeout: 5000})
            .then(function(data : Fetcher.FetchResponse<UpdateJwtResponse>){
                switch(data.reason){
                    case Fetcher.FetchResultStatus.FAILED_CONNECTION:
                    case Fetcher.FetchResultStatus.FAILED_OTHER:
                    case Fetcher.FetchResultStatus.FAILED_TIMEOUT:
                        RefreshLoginJwt();
                        break;
                    case Fetcher.FetchResultStatus.FAILED_LOGGEDOUT:
                    case Fetcher.FetchResultStatus.SUCCESS_NOT_2xx:
                        if(data.status == 401) return;
                        break;
                    case Fetcher.FetchResultStatus.SUCCESS_2xx:
                        if(data.response?.jwt == null) RefreshLoginJwt();
                        else{
                            window.localStorage.setItem("jwt:value", data.response?.jwt);
                            RefreshLoginJwt();
                        }
                }
            });
        }, nextRefresh);

        return true;
    }

    export function IsLoggedIn() : boolean
    {
        let jwt = window.localStorage.getItem("jwt:value");

        if(jwt == null || jwt == '') return false;

        let decoded_token = jwtDecode(jwt);
        let ts = Math.floor(Date.now() / 1000);
        let expireAt = decoded_token.exp ?? ts;

        console.log("IsLoggedIn = ", expireAt > ts)

        if(expireAt > ts){
            return true;
        }else{
            window.localStorage.removeItem("jwt:value");
            return false;
        }
    }
}   
