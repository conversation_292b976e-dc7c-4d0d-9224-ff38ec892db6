import DrawerTemplate, { DrawerFormTemplateProperties, DrawerTemplateErrorEmpty } from "@/templates/drawer-template";
import { AutoComplete, AutoCompleteProps, Button, Checkbox, DatePicker, Divider, Form, Input, Select, Typography } from "antd";
import React, { useEffect } from "react";
import '@ant-design/v5-patch-for-react-19';
import { Fetcher } from "@/functions/fetcher";
import SubmitButton from "@/components/submit-button";
import { Agent<PERSON><PERSON>s, AgentAliasController, AgentAliasCreateRequest } from "@/models/agentalias";
import { Agent, AgentController } from "@/models/agent";
import {
    LoadingOutlined,
    RobotOutlined,
    TagOutlined
} from '@ant-design/icons';
import AgentDescription from "@/components/agent/agent-description";
import { AgentTag } from "@/models/agenttag";

interface AgentAliasCreateDrawerParams {
    isOpen: boolean;
    onSuccess: (data: AgentAlias) => void;
}

const AgentAliasCreateDrawer: React.FC<AgentAliasCreateDrawerParams> = (params: AgentAliasCreateDrawerParams) => {
    const agentController: AgentController = new AgentController();

    const [form] = Form.useForm<AgentAliasCreateRequest>();
    const [drawerError, setDrawerError] = React.useState(DrawerTemplateErrorEmpty)
    const { data, trigger, isMutating } = agentController.useCreateAlias();

    const [agentSearch, setAgentSearch] = React.useState<string>();
    const { data: agentSearchData, isLoading: agentSearchIsLoading } = agentController.useSearch(agentSearch);
    const [autocompleteOptions, setAutocompleteOptions] = React.useState<AutoCompleteProps['options']>([]);
    const [agentToAssign, setAgentToAssign] = React.useState<Agent>();


    const useListTags = React.useCallback(() => agentController.useListTags({ count: 1000 }, agentToAssign?.agentId), [agentToAssign]);
    const { data: agentTagSearchData, isLoading: agentTagSearchIsLoading } = useListTags();

    useEffect(() => {
        if (data === undefined) return;
        setDrawerError(DrawerTemplateErrorEmpty);
        if (params.onSuccess != null) params.onSuccess(data);
    }, [data])

    const onFinish = (values: AgentAliasCreateRequest) => {
        let request: AgentAliasCreateRequest = { alias: values.alias, description: values.description, agentTag: values.agentTag }
        trigger({ body: request, urlPlaceholders: { agentId: agentToAssign?.agentId ?? '' } }).catch((reason: Fetcher.FetcherError) => {
            setDrawerError({ message: reason.message, title: 'Something went wrong with your request.', show: true });
        });
    };

    React.useEffect(() => {
        setAutocompleteOptions(agentSearchData?.entries.map((entry: Agent) => {
            return {
                label: <><AgentDescription agent={entry} layout="horizontal" /><Divider /></>,
                value: entry.name,
                agent: entry
            }
        }))
    }, [agentSearchData])

    return (
        <DrawerTemplate
            error={drawerError}
            isLoading={isMutating}
            title='Create an API Key'
        >
            <Form form={form} layout='vertical' onFinish={onFinish} scrollToFirstError={{ behavior: 'instant', block: 'end', focus: true }}>
                <Form.Item
                    label="Alias"
                    name="alias"
                    rules={[
                        { max: 64, message: 'Please input up to 64 characters.' },
                        { min: 3, message: 'Your alias must have at least 3 characters.' },
                        { required: true, message: 'Please input an alias for the agent' }
                    ]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{ required: true }, { min: 10 }, { max: 250, message: 'Please input a description of up to 250 characters' }]}
                    required
                >
                    <Input.TextArea rows={4} count={{
                        show: true,
                        max: 250,
                    }} />
                </Form.Item>
                <Form.Item label="Agent" layout="vertical" name="agentId" required rules={[{ required: true }]}
                    >
                    <AutoComplete
                        style={{ width: '100%' }}
                        prefix={(agentSearchIsLoading) ? <LoadingOutlined style={{ color: 'rgba(0,0,0,.25)' }} /> : <RobotOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                        placeholder="Agent Name"
                        options={autocompleteOptions}
                        onSelect={(value, option) => {
                            setAgentToAssign(option.agent);
                        }}
                        onSearch={(text) => { if (text.length >= 3) setAgentSearch(text); }}
                    />
                </Form.Item>
                <Form.Item label="Agent Tag" layout="vertical" name="agentTag" required rules={[{ required: true }]}>
                    <Select
                        disabled = {agentTagSearchIsLoading || agentSearchIsLoading || !agentSearchData}
                        showSearch
                        placeholder="Select a tag"
                        optionFilterProp="label"
                        options={agentTagSearchData?.entries.map((tag) => {
                            return {
                                value: tag.tag,
                                label: tag.tag,
                            }
                        })}
                    />
                </Form.Item>
                <Form.Item>
                    <SubmitButton form={form}>Create</SubmitButton>
                </Form.Item>
            </Form>
        </DrawerTemplate>
    )
}

export default AgentAliasCreateDrawer;