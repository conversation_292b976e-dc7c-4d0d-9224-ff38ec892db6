import { Flow } from "@/models/flow";
import { FlowTag, FlowTagController } from "@/models/flow/flow-tag";
import { Flow<PERSON><PERSON>s, FlowAliasController } from "@/models/flow/flow-alias";
import { Button, Descriptions, Space, Typography, List, Tag, Skeleton } from "antd";
import React from "react";
import {
    BuildOutlined,
    TagsOutlined,
    SignatureOutlined,
    StarFilled,
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";
import { useRouter } from "next/navigation";

interface FlowViewDrawerProps {
    flow: Flow;
    isOpen: boolean;
    onClose: () => void;
}

const FlowViewDrawer: React.FC<FlowViewDrawerProps> = ({ flow, isOpen, onClose }) => {
    const router = useRouter();
    const flowTagController = new FlowTagController();
    const flowAliasController = new FlowAliasController();
    
    const { data: tagsData, isLoading: isLoadingTags } = flowTagController.useListByFlow(flow.id);
    const { data: aliasesData, isLoading: isLoadingAliases } = flowAliasController.useListByFlow(flow.id);

    const handleOpenFlowBuilder = () => {
        router.push(`/platform/flows/${flow.id}/flowbuilder`);
        onClose();
    };

    return (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Typography.Title level={4}>Flow Information</Typography.Title>
            
            <Descriptions bordered column={1}>
                <Descriptions.Item label="Name">
                    {flow.name}
                </Descriptions.Item>
                <Descriptions.Item label="Description">
                    {flow.description}
                </Descriptions.Item>
                <Descriptions.Item label="Current Tag">
                    {flow.currentTagId || 'No tag set'}
                </Descriptions.Item>
                <Descriptions.Item label="Created At">
                    <TimeAgo date={flow.createdAt * 1000} locale="en-US" live={true} />
                </Descriptions.Item>
                <Descriptions.Item label="Last Modified">
                    <TimeAgo date={flow.lastChangeTimestamp * 1000} locale="en-US" live={true} />
                </Descriptions.Item>
            </Descriptions>

            <Space>
                <Button 
                    type="primary" 
                    icon={<BuildOutlined />}
                    onClick={handleOpenFlowBuilder}
                >
                    Open Flow Builder
                </Button>
                <Button onClick={onClose}>
                    Close
                </Button>
            </Space>

            <Typography.Title level={5}>
                <TagsOutlined /> Tags
            </Typography.Title>
            {isLoadingTags ? (
                <Skeleton active />
            ) : (
                <List
                    size="small"
                    dataSource={tagsData?.entries || []}
                    renderItem={(tag: FlowTag) => (
                        <List.Item>
                            <Space>
                                <span>{tag.tagName}</span>
                                <span>v{tag.version}</span>
                                {tag.isLatest && (
                                    <Tag color="gold" icon={<StarFilled />}>
                                        Latest
                                    </Tag>
                                )}
                                <span style={{ color: '#666' }}>
                                    <TimeAgo date={tag.createdAt * 1000} locale="en-US" />
                                </span>
                            </Space>
                        </List.Item>
                    )}
                    locale={{ emptyText: 'No tags created yet' }}
                />
            )}

            <Typography.Title level={5}>
                <SignatureOutlined /> Aliases
            </Typography.Title>
            {isLoadingAliases ? (
                <Skeleton active />
            ) : (
                <List
                    size="small"
                    dataSource={aliasesData?.entries || []}
                    renderItem={(alias: FlowAlias) => (
                        <List.Item>
                            <Space>
                                <strong>{alias.alias}</strong>
                                <span>→ {alias.tagName}</span>
                                <span style={{ color: '#666' }}>
                                    {alias.description}
                                </span>
                            </Space>
                        </List.Item>
                    )}
                    locale={{ emptyText: 'No aliases created yet' }}
                />
            )}
        </Space>
    );
};

export default FlowViewDrawer;
