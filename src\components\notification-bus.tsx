'use client'

import React from 'react';
import { notification } from 'antd';
import EventBus from '@/functions/event-bus';

export type NotificationType = 'success' | 'info' | 'warning' | 'error';

interface Props {};

export interface NotificationEvent {
    type : NotificationType;
    title : string;
    message : string;
};

const NotificationComponent: React.FC = () => {
    const [api, contextHolder] = notification.useNotification();
    
    const showNotificationWithIcon = (notificationEvent : NotificationEvent) => {
        api[notificationEvent.type]({
            message: notificationEvent.title,
            description: notificationEvent.message,
        });
    }

    React.useEffect(() => {
        EventBus.on<NotificationEvent>('notification', showNotificationWithIcon);
    }, [])

    const Component = () => <>{contextHolder}</>;
    //const Component = () => <></>;

    return (<Component />)
}

export default NotificationComponent;
