'use client';

import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { But<PERSON>, Card, Flex, Space, Tag } from 'antd';
import React from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { ParallelNodeModelData } from './parallel-node-model';
import { BaseNodeProps } from '../base-node';
import ParallelNodeDescription from './parallel-node-description';

export const ParallelNodeName = "parallel";

function ParallelNode(props: BaseNodeProps<ParallelNodeModelData>) {
    const outputCount = props.data.baseData.nodeData.outputCount || 2;
    
    return (
        <>
            <Card title={<Flex justify='stretch'>
                <div className='reactflow-wrapper'>
                    <Space>
                        {props.data.baseData.name}
                        <Tag color="default" bordered={false}>Parallel</Tag>
                    </Space>
                </div>
                <div>
                    <Button
                        style={{ width: '30px' }} type="default"
                        icon={<SettingOutlined />} size='small'
                        onClick={(event) => {
                            event.stopPropagation();
                            props.data.events?.onOpenDrawer();
                        }}
                    />
                </div>
            </Flex>}>
                <ParallelNodeDescription {...props.data.baseData} />
            </Card>
            <Handle type="target" position={Position.Left} id="input" />
            {Array.from({ length: outputCount }, (_, index) => (
                <Handle 
                    key={`output-${index}`}
                    type="source" 
                    position={Position.Right} 
                    id={`output-${index}`}
                    style={{ top: `${((index + 1) * 100) / (outputCount + 1)}%` }}
                />
            ))}
        </>
    );
}

export default React.memo((props: BaseNodeProps<ParallelNodeModelData>) => ParallelNode(props));
