'use client';

import { useCallback } from 'react';
import { Handle, NodeProps, Position, Node, NodeToolbar } from '@xyflow/react';
import { <PERSON>ton, Card, Collapse, Divider, Flex, Form, Input, InputNumber, Radio, Select, Space, Tag, Typography } from 'antd';
import React from 'react';
import { CheckOutlined, EditOutlined, SettingOutlined } from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import { SetAgentNodeModel, SetAgentNodeModelData } from './set-agent-model';
import { BaseNodeData, BaseNodeProps, NodeEvents } from '../base-node';
import SetAgentNodeDescription from './set-agent-description';
import SetAgentForm from './set-agent-form';

export const SetAgentNodeName = "setAgent";

function SetAgentNode(props: BaseNodeProps<SetAgentNodeModelData>) {

    return (
        <>
            <Card title={<Flex justify='stretch'>
                <div className='reactflow-wrapper'>
                    <Space>
                        {props.data.baseData.name}
                        <Tag color="default" bordered={false}>Set Agent</Tag>
                    </Space>
                </div>
                <div >
                    <Button style={{ width: '30px' }} type="default" icon={<SettingOutlined />} size='small' onClick={(event) => {
                        event.stopPropagation();
                        props.data.events?.onOpenDrawer();
                    }} />
                </div>
            </Flex>}>
                <SetAgentNodeDescription {...props.data.baseData} />
            </Card>
            <Handle type="target" position={Position.Left} id="trigger" />
        </>
    );
}

export default React.memo((props: BaseNodeProps<SetAgentNodeModelData>) => SetAgentNode(props));