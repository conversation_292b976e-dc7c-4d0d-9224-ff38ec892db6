import { ToolGroup, ToolGroupActions, ToolGroupController } from "@/models/toolgroup";
import { AgentTool, AgentToolLanguage, AgentToolStatus } from "@/models/agenttool";
import { Button, Card, Descriptions, List, Space, Tag, Typography, message, Skeleton } from "antd";
import {
    EditOutlined,
    DeleteOutlined,
    CopyOutlined,
    GroupOutlined,
    CodeOutlined,
    FunctionOutlined
} from '@ant-design/icons';
import React from "react";
import { useTimeAgo } from "next-timeago";

interface ToolGroupViewDrawerParams {
    toolGroup: ToolGroup;
    onEdit: (record: ToolGroup) => void;
    onDelete: (record: ToolGroup) => void;
    onDuplicate: (record: ToolGroup) => void;
}

const ToolGroupViewDrawer: React.FC<ToolGroupViewDrawerParams> = (params: ToolGroupViewDrawerParams) => {
    const controller = new ToolGroupController();
    const { data: groupWithToolsData, isLoading } = controller.useGetWithTools([params.toolGroup.id]);
    const { data: duplicateData, trigger: duplicateTrigger, isMutating: isDuplicating } = controller.useDuplicate(params.toolGroup.id);
    const { TimeAgo } = useTimeAgo();

    React.useEffect(() => {
        if (duplicateData) {
            message.success('Tool group duplicated successfully');
            params.onDuplicate(duplicateData);
        }
    }, [duplicateData]);

    const getLanguageColor = (language: AgentToolLanguage): string => {
        switch (language) {
            case AgentToolLanguage.JAVA:
                return 'orange';
            case AgentToolLanguage.CSHARP:
                return 'purple';
            case AgentToolLanguage.PYTHON:
                return 'green';
            default:
                return 'default';
        }
    };

    const getStatusColor = (status: AgentToolStatus): string => {
        switch (status) {
            case AgentToolStatus.DRAFT:
                return 'default';
            case AgentToolStatus.VALIDATING:
                return 'processing';
            case AgentToolStatus.READY:
                return 'success';
            case AgentToolStatus.FAILED:
                return 'error';
            case AgentToolStatus.DEPLOYING:
                return 'processing';
            case AgentToolStatus.DEPLOYED:
                return 'cyan';
            default:
                return 'default';
        }
    };

    const groupWithTools = groupWithToolsData?.[0];
    const tools = groupWithTools?.tools || [];

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            {/* Action Buttons */}
            <Space wrap>
                <Button
                    type="primary"
                    icon={<EditOutlined />}
                    onClick={() => params.onEdit(params.toolGroup)}
                    disabled={!controller.can(ToolGroupActions.EDIT, params.toolGroup)}
                >
                    Edit
                </Button>
                <Button
                    icon={<CopyOutlined />}
                    onClick={() => duplicateTrigger()}
                    loading={isDuplicating}
                    disabled={!controller.can(ToolGroupActions.DUPLICATE, params.toolGroup)}
                >
                    Duplicate
                </Button>
                <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => params.onDelete(params.toolGroup)}
                    disabled={!controller.can(ToolGroupActions.DELETE, params.toolGroup)}
                >
                    Delete
                </Button>
            </Space>

            {/* Group Information */}
            <Card title={<Space><GroupOutlined />Group Information</Space>}>
                <Descriptions column={2} bordered>
                    <Descriptions.Item label="Name">{params.toolGroup.name}</Descriptions.Item>
                    <Descriptions.Item label="Tools Count">
                        <Tag color="blue">
                            {params.toolGroup.toolIds?.length || 0} tools
                        </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="Description" span={2}>
                        {params.toolGroup.description}
                    </Descriptions.Item>
                    <Descriptions.Item label="Created">
                        <TimeAgo date={params.toolGroup.createdAt * 1000} locale="en-US" />
                    </Descriptions.Item>
                    <Descriptions.Item label="Last Modified">
                        <TimeAgo date={params.toolGroup.lastChangeTimestamp * 1000} locale="en-US" />
                    </Descriptions.Item>
                </Descriptions>
            </Card>

            {/* Tools in Group */}
            <Card 
                title={
                    <Space>
                        <FunctionOutlined />
                        Tools in Group ({tools.length})
                    </Space>
                }
            >
                {isLoading ? (
                    <Skeleton active />
                ) : tools.length === 0 ? (
                    <Typography.Text type="secondary">
                        No tools in this group yet. Edit the group to add tools.
                    </Typography.Text>
                ) : (
                    <List
                        dataSource={tools}
                        renderItem={(tool: AgentTool) => (
                            <List.Item>
                                <List.Item.Meta
                                    avatar={<CodeOutlined style={{ fontSize: '24px', color: '#1890ff' }} />}
                                    title={
                                        <Space>
                                            <Typography.Text strong>{tool.name}</Typography.Text>
                                            <Tag color={getLanguageColor(tool.language)}>
                                                {tool.language}
                                            </Tag>
                                            <Tag color={getStatusColor(tool.status)}>
                                                {tool.status}
                                            </Tag>
                                        </Space>
                                    }
                                    description={
                                        <Space direction="vertical" size="small">
                                            <Typography.Text>{tool.description}</Typography.Text>
                                            <Space>
                                                <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                                                    Inputs: {tool.inputs?.length || 0}
                                                </Typography.Text>
                                                {tool.version && (
                                                    <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                                                        Version: {tool.version}
                                                    </Typography.Text>
                                                )}
                                                <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                                                    Modified: <TimeAgo date={tool.lastChangeTimestamp * 1000} locale="en-US" />
                                                </Typography.Text>
                                            </Space>
                                        </Space>
                                    }
                                />
                            </List.Item>
                        )}
                    />
                )}
            </Card>

            {/* Summary Statistics */}
            {tools.length > 0 && (
                <Card title="Group Statistics">
                    <Space direction="vertical" style={{ width: '100%' }}>
                        <div>
                            <Typography.Text strong>Languages:</Typography.Text>
                            <div style={{ marginTop: 8 }}>
                                {Object.entries(
                                    tools.reduce((acc: Record<string, number>, tool) => {
                                        acc[tool.language] = (acc[tool.language] || 0) + 1;
                                        return acc;
                                    }, {})
                                ).map(([language, count]) => (
                                    <Tag key={language} color={getLanguageColor(language as AgentToolLanguage)}>
                                        {language}: {count}
                                    </Tag>
                                ))}
                            </div>
                        </div>
                        <div>
                            <Typography.Text strong>Status Distribution:</Typography.Text>
                            <div style={{ marginTop: 8 }}>
                                {Object.entries(
                                    tools.reduce((acc: Record<string, number>, tool) => {
                                        acc[tool.status] = (acc[tool.status] || 0) + 1;
                                        return acc;
                                    }, {})
                                ).map(([status, count]) => (
                                    <Tag key={status} color={getStatusColor(status as AgentToolStatus)}>
                                        {status}: {count}
                                    </Tag>
                                ))}
                            </div>
                        </div>
                        <div>
                            <Typography.Text strong>Total Input Parameters:</Typography.Text>
                            <Tag color="blue" style={{ marginLeft: 8 }}>
                                {tools.reduce((total, tool) => total + (tool.inputs?.length || 0), 0)}
                            </Tag>
                        </div>
                    </Space>
                </Card>
            )}
        </Space>
    );
};

export default ToolGroupViewDrawer;
