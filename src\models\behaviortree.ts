import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export interface BehaviorTree extends IBasemodel {
    id: string;
    name: string;
    description: string;
    agentId?: string;
    treeDefinition?: string;
}

export enum BehaviorTreeActions {
    EDIT,
    DELETE,
    CREATE,
    BEHAVIORBUILDER
}

export interface BehaviorTreeCreateRequest {
    name: string;
    description: string;
    agentId?: string;
}

export class BehaviorTreeController implements IBasemodelController<BehaviorTree, BehaviorTreeActions> {
    getId: (item: BehaviorTree) => string = (item) => item.id;
    useGet = (ids: string[], options?: SWRConfiguration) => Fetcher2.SWRMultiTemplate<BehaviorTree>(ids.map((id) => APIRoutes.BehaviorTreeController.GET.replace("{id}", id)), { method: 'GET' }, options)
    can = (action: BehaviorTreeActions, onItem: BehaviorTree) => {
        return true;
    }
    useList = (request: ListRequest) => Fetcher2.SWRTemplate<ListResponse<BehaviorTree>>(APIRoutes.BehaviorTreeController.LIST, {method: 'GET', queryString: request})
    
    useDelete = (id: string) => Fetcher2.SWRMutationTemplate<BehaviorTree>(APIRoutes.BehaviorTreeController.DELETE, {method: 'DELETE', urlPlaceholders: {id: id}})

    useCreate = () => Fetcher2.SWRMutationTemplate<BehaviorTree>(APIRoutes.BehaviorTreeController.CREATE, {method: 'POST' })

    useUpdate = () => Fetcher2.SWRMutationTemplate<BehaviorTree>(APIRoutes.BehaviorTreeController.EDIT, {method: 'PUT'})

    useListByAgent = (agentId: string) => Fetcher2.SWRTemplate<ListResponse<BehaviorTree>>(APIRoutes.BehaviorTreeController.LIST, {method: 'GET', queryString: {agentId: agentId}})
}

