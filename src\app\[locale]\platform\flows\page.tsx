'use client'

import { Flow, FlowController } from "@/models/flow";
import FlowList from "./list/flow-list";
import { <PERSON><PERSON>, Divider, Drawer, Space, Typography, Tabs } from "antd";
import ConfirmDeleteModal from "@/components/confirm-delete-modal";
import React from "react";
import {
    PlusOutlined,
    TagsOutlined,
    SignatureOutlined,
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import FlowPutDrawer from "./drawers/put-drawer";
import FlowViewDrawer from "./drawers/view-drawer";
import FlowTagDrawer from "./drawers/tag-drawer";
import FlowAliasDrawer from "./drawers/alias-drawer";
import { useRouter } from "next/navigation";
import { FlowTag, FlowTagController } from "@/models/flow/flow-tag";
import { FlowAlias, FlowAliasController } from "@/models/flow/flow-alias";
import FlowTagList from "./list/flow-tag-list";
import FlowAliasList from "./list/flow-alias-list";

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions {
    flow?: Flow;
    flowTag?: FlowTag;
    flowAlias?: FlowAlias;
    open: boolean;
    type: 'flow' | 'tag' | 'alias';
}

const FlowsComponent: React.FC = () => {
    const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
    const [mutateFlows, setMutateFlows] = React.useState<Flow[]>([]);
    const [mutateTags, setMutateTags] = React.useState<FlowTag[]>([]);
    const [mutateAliases, setMutateAliases] = React.useState<FlowAlias[]>([]);
    const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({ 
        open: false, 
        type: 'flow' 
    });
    const [activeTab, setActiveTab] = React.useState('flows');
    const router = useRouter();

    const onCloseDrawer = () => {
        setDrawerOptions(emptyDrawer);
    };

    // Flow operations
    const onCreateFlow = () => {
        setDrawerOptions({ 
            title: "Create Flow", 
            isOpen: true, 
            component: <FlowPutDrawer isOpen={true} onSuccess={onFlowSuccess} mode='create' /> 
        })
    }

    const onViewFlow = (record: Flow) => {
        setDrawerOptions({ 
            title: `Flow Details - ${record.name}`, 
            isOpen: true, 
            component: <FlowViewDrawer flow={record} isOpen={true} onClose={onCloseDrawer} /> 
        })
    }

    const onEditFlow = (record: Flow) => {
        setDrawerOptions({ 
            title: "Edit Flow", 
            isOpen: true, 
            component: <FlowPutDrawer isOpen={true} onSuccess={onFlowSuccess} mode='edit' flow={record} /> 
        })
    }

    const onFlowBuilder = (record: Flow) => {
        router.push(`/platform/flows/${record.id}/flowbuilder`);
    }

    const onCreateTag = (record: Flow) => {
        setDrawerOptions({ 
            title: `Create Tag for ${record.name}`, 
            isOpen: true, 
            component: <FlowTagDrawer isOpen={true} onSuccess={onTagSuccess} mode='create' flow={record} /> 
        })
    }

    const onCreateAlias = (record: Flow) => {
        setDrawerOptions({ 
            title: `Create Alias for ${record.name}`, 
            isOpen: true, 
            component: <FlowAliasDrawer isOpen={true} onSuccess={onAliasSuccess} mode='create' flow={record} /> 
        })
    }

    const onFlowSuccess = (record: Flow) => {
        setMutateFlows([record]);
        onCloseDrawer();
    }

    const onDeleteFlow = (record: Flow) => {
        setDeleteModalOptions({ open: true, flow: record, type: 'flow' })
    }

    // Tag operations
    const onEditTag = (record: FlowTag) => {
        setDrawerOptions({ 
            title: "Edit Tag", 
            isOpen: true, 
            component: <FlowTagDrawer isOpen={true} onSuccess={onTagSuccess} mode='edit' flowTag={record} /> 
        })
    }

    const onTagSuccess = (record: FlowTag) => {
        setMutateTags([record]);
        onCloseDrawer();
    }

    const onDeleteTag = (record: FlowTag) => {
        setDeleteModalOptions({ open: true, flowTag: record, type: 'tag' })
    }

    // Alias operations
    const onEditAlias = (record: FlowAlias) => {
        setDrawerOptions({ 
            title: "Edit Alias", 
            isOpen: true, 
            component: <FlowAliasDrawer isOpen={true} onSuccess={onAliasSuccess} mode='edit' flowAlias={record} /> 
        })
    }

    const onAliasSuccess = (record: FlowAlias) => {
        setMutateAliases([record]);
        onCloseDrawer();
    }

    const onDeleteAlias = (record: FlowAlias) => {
        setDeleteModalOptions({ open: true, flowAlias: record, type: 'alias' })
    }

    // Delete operations
    const onDeleteConfirm = (record: Flow | FlowTag | FlowAlias) => {
        setDeleteModalOptions({ open: false, type: 'flow' });
        if (deleteModalOptions.type === 'flow') {
            setMutateFlows([record as Flow]);
        } else if (deleteModalOptions.type === 'tag') {
            setMutateTags([record as FlowTag]);
        } else if (deleteModalOptions.type === 'alias') {
            setMutateAliases([record as FlowAlias]);
        }
    }

    const onDeleteCancel = () => {
        setDeleteModalOptions({ open: false, type: 'flow' });
    }

    const getDeleteController = () => {
        switch (deleteModalOptions.type) {
            case 'flow':
                return new FlowController();
            case 'tag':
                return new FlowTagController();
            case 'alias':
                return new FlowAliasController();
            default:
                return new FlowController();
        }
    }

    const getDeleteObject = () => {
        switch (deleteModalOptions.type) {
            case 'flow':
                return deleteModalOptions.flow;
            case 'tag':
                return deleteModalOptions.flowTag;
            case 'alias':
                return deleteModalOptions.flowAlias;
            default:
                return undefined;
        }
    }

    const tabItems = [
        {
            key: 'flows',
            label: 'Flows',
            children: (
                <FlowList
                    onClick={onViewFlow}
                    onDelete={onDeleteFlow}
                    onEdit={onEditFlow}
                    onFlowBuilder={onFlowBuilder}
                    onCreateTag={onCreateTag}
                    onCreateAlias={onCreateAlias}
                    mutateObjects={mutateFlows}
                />
            )
        },
        {
            key: 'tags',
            label: (
                <span>
                    <TagsOutlined />
                    Tags
                </span>
            ),
            children: (
                <FlowTagList
                    onEdit={onEditTag}
                    onDelete={onDeleteTag}
                    mutateObjects={mutateTags}
                />
            )
        },
        {
            key: 'aliases',
            label: (
                <span>
                    <SignatureOutlined />
                    Aliases
                </span>
            ),
            children: (
                <FlowAliasList
                    onEdit={onEditAlias}
                    onDelete={onDeleteAlias}
                    mutateObjects={mutateAliases}
                />
            )
        }
    ];

    return (
        <>
            <ConfirmDeleteModal
                controller={getDeleteController()}
                open={deleteModalOptions.open}
                objectToDelete={getDeleteObject()}
                onDelete={onDeleteConfirm}
                onCancel={onDeleteCancel}
            />
            
            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                <Space direction="horizontal" size="middle" style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography.Title level={2}>
                        Flows
                    </Typography.Title>
                    {activeTab === 'flows' && (
                        <Button size='large' type='primary' onClick={onCreateFlow}>
                            <PlusOutlined />Create Flow
                        </Button>
                    )}
                </Space>
                <Divider />
                
                <Tabs 
                    activeKey={activeTab} 
                    onChange={setActiveTab}
                    items={tabItems}
                />
            </Space>

            <Drawer
                title={drawerOptions.title}
                placement="right"
                size="large"
                onClose={onCloseDrawer}
                open={drawerOptions.isOpen}
                extra={
                    <Space>
                        <Button onClick={onCloseDrawer}>Cancel</Button>
                    </Space>
                }
            >
                {drawerOptions.component}
            </Drawer>
        </>
    )
}

export default FlowsComponent;
