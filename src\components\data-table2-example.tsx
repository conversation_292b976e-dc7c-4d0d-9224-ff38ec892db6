import React from 'react';
import { <PERSON><PERSON>, Space, TableColumnsType } from 'antd';
import { DataTable2 } from './data-table2';
import { KnowledgeBase, KnowledgebaseController } from '@/models/knowledgebase';
import { useLazyDataSourceList } from './lazydatasource/lazydatasource-list';
import { ItemMutation, MutationOperation } from './lazydatasource/lazymutation';
import { ItemUpdateStrategy } from './lazydatasource/lazydatasource';

// Example usage of DataTable2 with LazyDataSource
export function DataTable2Example() {
    const controller = new KnowledgebaseController();
    const dataSource = useLazyDataSourceList(controller, 25); // Load 25 items per request
    const [mutations, setMutations] = React.useState<ItemMutation<KnowledgeBase>[]>([]);
    const [invalidate, setInvalidate] = React.useState<boolean>(false);
    const [pageSize, setPageSize] = React.useState<number>(10);
    const [currentPage, setCurrentPage] = React.useState<number>(1);

    // Configure auto-loading
    React.useEffect(() => {
        dataSource.setAutoLoadUntilExhaustion(false); // Don't auto-load all data
    }, [dataSource]);

    // Example table columns
    const columns: TableColumnsType<KnowledgeBase> = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: 'Description',
            dataIndex: 'description',
            key: 'description',
        },
        {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (_, record) => (
                <Space>
                    <Button 
                        size="small" 
                        onClick={() => handleReplaceItem(record)}
                    >
                        Replace
                    </Button>
                    <Button 
                        size="small" 
                        onClick={() => handleMergeItem(record)}
                    >
                        Merge
                    </Button>
                    <Button 
                        size="small" 
                        danger
                        onClick={() => handleDeleteItem(record)}
                    >
                        Delete
                    </Button>
                </Space>
            ),
        },
    ];

    // Example mutation handlers
    const handleReplaceItem = (item: KnowledgeBase) => {
        const updatedItem = { ...item, name: `${item.name} (Updated)` };
        setMutations([{
            operation: MutationOperation.REPLACE,
            item: updatedItem
        }]);
        // Clear mutations after applying
        setTimeout(() => setMutations([]), 100);
    };

    const handleMergeItem = (item: KnowledgeBase) => {
        setMutations([{
            operation: MutationOperation.MERGE,
            partialItem: { 
                kbId: item.kbId,
                description: `${item.description} (Merged)` 
            }
        }]);
        // Clear mutations after applying
        setTimeout(() => setMutations([]), 100);
    };

    const handleDeleteItem = (item: KnowledgeBase) => {
        setMutations([{
            operation: MutationOperation.DELETE,
            id: item.kbId
        }]);
        // Clear mutations after applying
        setTimeout(() => setMutations([]), 100);
    };

    const handleInvalidate = () => {
        setInvalidate(true);
        // Reset invalidate flag
        setTimeout(() => setInvalidate(false), 100);
    };

    const handleLoadAll = () => {
        dataSource.loadAll();
    };

    return (
        <div>
            <Space style={{ marginBottom: 16 }}>
                <Button onClick={handleInvalidate}>
                    Invalidate Data
                </Button>
                <Button onClick={handleLoadAll}>
                    Load All Data
                </Button>

                <Button onClick={() => setPageSize(5)}>
                    Page Size: 5
                </Button>
                <Button onClick={() => setPageSize(10)}>
                    Page Size: 10
                </Button>
                <Button onClick={() => setPageSize(20)}>
                    Page Size: 20
                </Button>

                <div>
                    Loaded: {dataSource.getLoadedCount()} / {dataSource.getTotalCount()} | Page: {currentPage} | Size: {pageSize}
                </div>
            </Space>
            
            <DataTable2<KnowledgeBase>
                dataSource={dataSource}
                itemUpdateInterval={10000}
                itemUpdateStrategy={ItemUpdateStrategy.REPLACE}
                tableColumns={columns}
                shouldInvalidate={(entry) => entry.status === 'PROCESSING'}
                mutations={mutations}
                invalidate={invalidate}
                pageSize={pageSize}
                currentPage={currentPage}
                onPageChange={(page, size) => {
                    console.log('Page changed:', page, 'Size:', size);
                    setCurrentPage(page);
                    setPageSize(size);
                }}
                onDataChange={(entries) => {
                    console.log('Data changed:', entries.length, 'items');
                }}
                onItemsValidatingChange={(isValidating, validatingIds) => {
                    console.log('Items validating:', isValidating, validatingIds);
                }}
                noDataDetails={(isError) => (
                    <div>
                        {isError ? 'Error loading data' : 'No knowledge bases found'}
                    </div>
                )}
            />
        </div>
    );
}

export default DataTable2Example;
