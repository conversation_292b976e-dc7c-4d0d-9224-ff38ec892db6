import { <PERSON><PERSON><PERSON><PERSON>, MC<PERSON><PERSON>r<PERSON><PERSON>roller, MCPServerStatus } from "@/models/mcpserver";
import { Button, Dropdown, Space, Tag } from "antd";
import type { TableColumnsType } from 'antd';
import React from "react";
import {
    EllipsisOutlined,
    EyeOutlined,
    EditOutlined,
    DeleteOutlined,
    PlayCircleOutlined,
    StopOutlined,
    ReloadOutlined,
} from '@ant-design/icons';
import DataTable from "@/components/data-table";
import { useTimeAgo } from "next-timeago";

interface MCPServerListProps {
    mutateObjects?: MCPServer[];
    onClick?: (record: MCPServer) => void;
    onEdit?: (record: MCPServer) => void;
    onDelete?: (record: MCPServer) => void;
    onDeploy?: (record: MCPServer) => void;
    onStop?: (record: MCPServer) => void;
    onRestart?: (record: MCPServer) => void;
}

const MCPServerList: React.FC<MCPServerListProps> = (params) => {
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState<boolean>(false);
    const mcpServerController = new MCPServerController();

    const contextMenuProps = (record: MCPServer) => {
        const items = [
            {
                key: 'view',
                label: 'View Details',
                icon: <EyeOutlined />,
                onClick: () => {
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }
            }
        ];

        if (mcpServerController.can('EDIT', record)) {
            items.push({
                key: 'edit',
                label: 'Edit',
                icon: <EditOutlined />,
                onClick: () => {
                    if (params.onEdit) {
                        params.onEdit(record);
                    }
                }
            });
        }

        if (mcpServerController.can('DEPLOY', record)) {
            items.push({
                key: 'deploy',
                label: 'Deploy',
                icon: <PlayCircleOutlined />,
                onClick: () => {
                    if (params.onDeploy) {
                        params.onDeploy(record);
                    }
                }
            });
        }

        if (mcpServerController.can('STOP', record)) {
            items.push({
                key: 'stop',
                label: 'Stop',
                icon: <StopOutlined />,
                onClick: () => {
                    if (params.onStop) {
                        params.onStop(record);
                    }
                }
            });
        }

        if (mcpServerController.can('RESTART', record)) {
            items.push({
                key: 'restart',
                label: 'Restart',
                icon: <ReloadOutlined />,
                onClick: () => {
                    if (params.onRestart) {
                        params.onRestart(record);
                    }
                }
            });
        }

        if (mcpServerController.can('DELETE', record)) {
            items.push(
                { type: 'divider' },
                {
                    key: 'delete',
                    label: 'Delete',
                    icon: <DeleteOutlined />,
                    danger: true,
                    onClick: () => {
                        if (params.onDelete) {
                            params.onDelete(record);
                        }
                    }
                }
            );
        }

        return { items };
    };

    const getStatusColor = (status: MCPServerStatus) => {
        switch (status) {
            case MCPServerStatus.CREATED:
                return 'default';
            case MCPServerStatus.DEPLOYING:
                return 'processing';
            case MCPServerStatus.RUNNING:
                return 'success';
            case MCPServerStatus.STOPPED:
                return 'default';
            case MCPServerStatus.FAILED:
                return 'error';
            case MCPServerStatus.UPDATING:
                return 'processing';
            default:
                return 'default';
        }
    };

    const columns: TableColumnsType<MCPServer> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Name', 
            dataIndex: 'name', 
            render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{value}</a>;
            }
        },
        { 
            title: 'Description', 
            dataIndex: 'description',
            ellipsis: true
        },
        {
            title: 'Flow', 
            dataIndex: 'flowName',
            render: (value) => value || 'N/A'
        },
        {
            title: 'Flow Alias', 
            dataIndex: 'flowAlias'
        },
        {
            title: 'Status', 
            dataIndex: 'status',
            render: (status: MCPServerStatus) => (
                <Tag color={getStatusColor(status)}>
                    {status}
                </Tag>
            )
        },
        {
            title: 'Endpoint', 
            dataIndex: 'endpoint',
            render: (value) => value || 'Not deployed'
        },
        {
            title: 'Deployed At', 
            dataIndex: 'deployedAt',
            render: (value) => value ? <TimeAgo date={value * 1000} locale="en-US" live={true} /> : 'Not deployed'
        },
        {
            title: 'Created At', 
            dataIndex: 'createdAt', 
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<MCPServer, MCPServerController>
            controller={new MCPServerController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: MCPServer) => 
                entry.status === MCPServerStatus.DEPLOYING || 
                entry.status === MCPServerStatus.UPDATING
            }
            mutateItems={params.mutateObjects}
            rowKey="id"
        />
    )
}

export default MCPServerList;
