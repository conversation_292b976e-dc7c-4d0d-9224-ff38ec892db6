import { Button, Dropdown, MenuProps, Space, TableColumnsType, Tag } from "antd";
import { useTimeAgo } from "next-timeago";
import React from "react";
import { SwapOutlined, DeleteOutlined, EllipsisOutlined, EditOutlined } from "@ant-design/icons";
import DataTable from "@/components/data-table";
import { Flow, FlowController, FlowActions } from "@/models/flow";

interface FlowListParams {
    onClick: (record: Flow) => void;
    onEdit: (record: Flow) => void;
    onBuilder: (record: Flow) => void;
    onDelete: (record: Flow) => void;
    mutateObjects?: Flow[];
}



const FlowList: React.FC<FlowListParams> = (params: FlowListParams) => {

    const flowController: FlowController = new FlowController();
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState(false);
    const { TimeAgo } = useTimeAgo();

    const contextMenuItems = (record: Flow): MenuProps['items'] => ([
        {
            label: 'Edit',
            key: 'edit',
            icon: <EditOutlined />,
            onClick: () => { if (params.onEdit) params.onEdit(record); },
            disabled: !flowController.can(FlowActions.EDIT, record)
        },
        {
            label: 'Flow Builder',
            key: 'builder',
            icon: <EditOutlined />,
            onClick: () => { if (params.onBuilder) params.onBuilder(record); },
            disabled: !flowController.can(FlowActions.FLOWBUILDER, record)
        },
        {
            label: 'Delete',
            key: 'delete',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => { if (params.onDelete) params.onDelete(record); },
            disabled: !flowController.can(FlowActions.DELETE, record)
        },
    ]);

    const contextMenuProps = (record: Flow) => ({
        items: contextMenuItems(record)
    });

    const columns: TableColumnsType<Flow> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        { title: 'Name', render: (value, record) => {
            return <Space>
                <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{record.name}</a>
            </Space>
        }},
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'Last Modified', dataIndex: 'lastModifiedTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<Flow, FlowController>
            controller={new FlowController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: Flow) => false}
            mutateItems={params.mutateObjects}
            rowKey={(record : Flow) => record.id}
        />
    );
}

export default FlowList;
