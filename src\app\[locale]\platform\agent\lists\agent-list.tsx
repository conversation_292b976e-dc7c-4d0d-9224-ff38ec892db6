import { But<PERSON>, Dropdown, MenuProps, Space, Spin, TableColumnsType, theme } from "antd";
import {
    EditOutlined,
    RocketOutlined,
    DeleteOutlined,
    EllipsisOutlined,
    LoadingOutlined,
    ApartmentOutlined,
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";
import DataTable from "@/components/data-table";
import React from "react";
import { Agent, AgentActions, AgentController, AgentStatus, AgentType } from "@/models/agent";
import { GetAgentModelFromType, GetAgentStatusTag } from "@/components/agent/agent-description";


interface AgentListParams {
    onClick: (record: Agent) => void;
    onEdit: (record: Agent) => void;
    onDeploy: (record: Agent) => void;
    onDelete: (record: Agent) => void;
    onEditBehavior: (record: Agent) => void;
    mutateObjects?: Agent[];
}



const AgentList: React.FC<AgentListParams> = (params: AgentListParams) => {
    const agentController: AgentController = new AgentController();
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState(false);
    const { TimeAgo } = useTimeAgo();

    const contextMenuItems = (record: Agent): MenuProps['items'] => ([
        {
            label: 'Edit',
            key: 'edit',
            icon: <EditOutlined />,
            onClick: () => { if (params.onEdit) params.onEdit(record); },
            disabled: !agentController.can(AgentActions.EDIT, record)
        },
        {
            label: 'Edit Behavior',
            key: 'edit-behavior',
            icon: <ApartmentOutlined />,
            onClick: () => { if (params.onEditBehavior) params.onEditBehavior(record); },
            disabled: !agentController.can(AgentActions.EDIT_BEHAVIOR, record)
        },
        {
            label: 'Deploy',
            key: 'deploy',
            icon: <RocketOutlined />,
            onClick: () => { if (params.onDeploy) params.onDeploy(record); },
        },
        {
            label: 'Delete',
            key: 'delete',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => { if (params.onDelete) params.onDelete(record); },
            disabled: !agentController.can(AgentActions.DELETE, record)
        },
    ]);

    const contextMenuProps = (record: Agent) => ({
        items: contextMenuItems(record)
    });

    const columns: TableColumnsType<Agent> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Name', dataIndex: 'name', render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{value}</a>;
            }
        },
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'AI Model', dataIndex: 'agentType', render: (type: AgentType, record: Agent) => {
                return GetAgentModelFromType(type);
            }
        },
        {
            title: 'Status', dataIndex: 'status', render: (status: AgentStatus, record: Agent, index) => {
                const text = GetAgentStatusTag(status);
                return (
                    <Space size={'small'}>
                        {text}
                        <Spin key={record.agentId} indicator={<LoadingOutlined spin />} spinning={pendingData.includes(record.agentId) && isLoadingData} />
                    </Space>
                );
            },
            shouldCellUpdate: (record: Agent, prevRecord: Agent) => {
                if (record === undefined || prevRecord === undefined) return false;
                return (record.status != AgentStatus.READY || (pendingData.includes(record.agentId) && isLoadingData) || record.status != prevRecord.status)
            },
            width: 160,
        },
        {
            title: 'Last Modified', dataIndex: 'lastChangeTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<Agent, AgentController>
            controller={new AgentController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: Agent) => entry.status != AgentStatus.READY}
            mutateItems={params.mutateObjects}
            rowKey="agentId"
        />
    )


}

export default AgentList;