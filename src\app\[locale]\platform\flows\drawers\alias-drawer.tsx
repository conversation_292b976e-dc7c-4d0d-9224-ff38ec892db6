import { Flow } from "@/models/flow";
import { <PERSON><PERSON><PERSON>s, FlowAliasController, FlowAliasCreateRequest, FlowAliasUpdateRequest } from "@/models/flow/flow-alias";
import { FlowTag, FlowTagController } from "@/models/flow/flow-tag";
import { Button, Form, Input, Space, message, Select, Alert, Skeleton } from "antd";
import React from "react";

interface FlowAliasDrawerProps {
    isOpen: boolean;
    onSuccess: (flowAlias: FlowAlias) => void;
    mode: 'create' | 'edit';
    flow?: Flow;
    flowAlias?: FlowAlias;
}

const FlowAliasDrawer: React.FC<FlowAliasDrawerProps> = ({ isOpen, onSuccess, mode, flow, flowAlias }) => {
    const [form] = Form.useForm();
    const flowAliasController = new FlowAliasController();
    const flowTagController = new FlowTagController();
    const { trigger: createTrigger, isMutating: isCreating } = flowAliasController.useCreate();
    const { trigger: updateTrigger, isMutating: isUpdating } = flowAliasController.useUpdate();

    // Get tags for the flow
    const flowId = flow?.id || flowAlias?.flowId;
    const { data: tagsData, isLoading: isLoadingTags } = flowTagController.useListByFlow(flowId || '');

    React.useEffect(() => {
        if (isOpen) {
            if (mode === 'edit' && flowAlias) {
                form.setFieldsValue({
                    alias: flowAlias.alias,
                    description: flowAlias.description,
                    flowTagId: flowAlias.flowTagId
                });
            } else {
                form.resetFields();
            }
        }
    }, [isOpen, mode, flowAlias, form]);

    const handleSubmit = async (values: any) => {
        try {
            let result;
            if (mode === 'create' && flow) {
                const request: FlowAliasCreateRequest = {
                    flowId: flow.id,
                    alias: values.alias,
                    description: values.description,
                    flowTagId: values.flowTagId
                };
                result = await createTrigger(request);
            } else if (mode === 'edit' && flowAlias) {
                const request: FlowAliasUpdateRequest = {
                    id: flowAlias.id,
                    alias: values.alias,
                    description: values.description,
                    flowTagId: values.flowTagId
                };
                result = await updateTrigger(request);
            }

            if (result) {
                message.success(`Alias ${mode === 'create' ? 'created' : 'updated'} successfully`);
                onSuccess(result);
            }
        } catch (error) {
            message.error(`Failed to ${mode} alias`);
        }
    };

    const isLoading = isCreating || isUpdating;

    const tagOptions = (tagsData?.entries || []).map((tag: FlowTag) => ({
        label: `${tag.tagName} (v${tag.version})${tag.isLatest ? ' - Latest' : ''}`,
        value: tag.id
    }));

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            disabled={isLoading}
        >
            {mode === 'create' && flow && (
                <Alert
                    message={`Creating alias for flow: ${flow.name}`}
                    type="info"
                    style={{ marginBottom: 16 }}
                />
            )}

            <Form.Item
                name="alias"
                label="Alias Name"
                rules={[
                    { required: true, message: 'Please enter an alias name' },
                    { min: 2, message: 'Alias name must be at least 2 characters' }
                ]}
            >
                <Input placeholder="Enter alias name (e.g., production, staging, latest)" />
            </Form.Item>

            <Form.Item
                name="description"
                label="Description"
                rules={[
                    { required: true, message: 'Please enter a description' }
                ]}
            >
                <Input.TextArea 
                    rows={3} 
                    placeholder="Enter alias description"
                />
            </Form.Item>

            <Form.Item
                name="flowTagId"
                label="Flow Tag"
                rules={[
                    { required: true, message: 'Please select a flow tag' }
                ]}
            >
                {isLoadingTags ? (
                    <Skeleton.Input active />
                ) : (
                    <Select
                        placeholder="Select a flow tag"
                        options={tagOptions}
                        showSearch
                        filterOption={(input, option) =>
                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                    />
                )}
            </Form.Item>

            {tagOptions.length === 0 && !isLoadingTags && (
                <Alert
                    message="No tags available"
                    description="You need to create at least one tag before creating an alias."
                    type="warning"
                    style={{ marginBottom: 16 }}
                />
            )}

            <Form.Item>
                <Space>
                    <Button 
                        type="primary" 
                        htmlType="submit" 
                        loading={isLoading}
                        disabled={tagOptions.length === 0}
                    >
                        {mode === 'create' ? 'Create' : 'Update'} Alias
                    </Button>
                </Space>
            </Form.Item>
        </Form>
    );
};

export default FlowAliasDrawer;
