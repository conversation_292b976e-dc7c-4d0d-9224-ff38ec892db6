# DataTable2 Component

DataTable2 is an enhanced version of the original DataTable component that supports lazy loading, advanced item mutations, automatic item updates, and better pagination handling through the LazyDataSource interface.

## Key Features

1. **Lazy Data Loading**: Load data on-demand with pagination support
2. **Item Mutations**: Replace, merge, or delete items with three different strategies
3. **Auto-loading**: Option to automatically load all data until exhaustion
4. **Smart Pagination**: Automatically loads data when users navigate to unloaded pages
5. **SWR Integration**: Built on top of SWR for efficient data fetching and caching
6. **Automatic Item Updates**: Built-in support for refreshing items that need updates
7. **404 Error Handling**: Automatically removes items that are deleted on the server
8. **Configurable Update Strategy**: Choose between merge or replace for item updates
9. **Dynamic Page Size**: Support for changing page size with automatic data loading

## Components

### LazyDataSource Interface

The core interface that handles data loading and management:

```typescript
interface LazyDataSource<T extends IBasemodel> {
    // Data access
    getData(): T[];
    getTotalCount(): number;
    getLoadedCount(): number;
    
    // Loading state
    isLoading(): boolean;
    
    // Data operations
    loadMore(): Promise<void>;
    loadUntilPage(page: number, pageSize: number): Promise<void>;
    loadAll(): Promise<void>;
    invalidate(): void;
    
    // Item mutations
    replaceItem(item: T): void;
    mergeItem(item: Partial<T>): void;
    deleteItem(id: string): void;
    
    // Configuration
    setMinItemsPerRequest(count: number): void;
    setAutoLoadUntilExhaustion(enabled: boolean): void;
    
    // Events
    onDataChange(callback: (data: T[]) => void): void;
    onLoadingChange(callback: (isLoading: boolean) => void): void;
}
```

### LazyDataSourceList Implementation

A concrete implementation that works with `IBasemodelController`:

```typescript
const dataSource = useLazyDataSourceList(controller, 50); // 50 items per request
```

### Item Mutations

Three types of mutations are supported:

1. **REPLACE**: Completely replace an item by ID
2. **MERGE**: Deep merge partial data into an existing item
3. **DELETE**: Remove an item from the table

```typescript
const mutations: ItemMutation<T>[] = [
    {
        operation: MutationOperation.REPLACE,
        item: updatedItem
    },
    {
        operation: MutationOperation.MERGE,
        partialItem: { id: 'item-id', name: 'New Name' }
    },
    {
        operation: MutationOperation.DELETE,
        id: 'item-id-to-delete'
    }
];
```

## Usage Example

```typescript
import { DataTable2, useLazyDataSourceList, MutationOperation } from './data-table2';

function MyComponent() {
    const controller = new MyController();
    const dataSource = useLazyDataSourceList(controller, 25);
    const [mutations, setMutations] = useState([]);

    const columns = [
        { title: 'Name', dataIndex: 'name', key: 'name' },
        // ... other columns
    ];

    return (
        <DataTable2
            dataSource={dataSource}
            itemUpdateInterval={10000}
            tableColumns={columns}
            shouldInvalidate={(entry) => entry.status === 'PROCESSING'}
            mutations={mutations}
            onDataChange={(entries) => console.log('Data changed:', entries)}
        />
    );
}
```

## Configuration Options

- `minItemsPerRequest`: Number of items to load per request (default: 50)
- `autoLoadUntilExhaustion`: Whether to automatically load all data (default: false)
- `itemUpdateInterval`: Interval for refreshing items that need updates (default: 10000ms)
- `itemUpdateStrategy`: Strategy for updating items - MERGE or REPLACE (default: REPLACE)
- `shouldInvalidate`: Function to determine which items need periodic updates
- `pageSize`: Number of items per page (default: 10)
- `currentPage`: Current page number (default: 1)
- `onPageChange`: Callback when page or page size changes

## Item Update Management

The LazyDataSource now handles automatic item updates:

1. **Update Cycle**: Items are updated in cycles with configurable intervals
2. **One Request at a Time**: Only one item update request is made at a time
3. **Timing Control**: Minimum 1ms wait between cycles, respecting itemUpdateInterval
4. **404 Handling**: Items that return 404 errors are automatically removed
5. **Update Strategy**: Choose between merging or replacing updated items

## Page Size Management

The DataTable2 component supports dynamic page size changes:

1. **Configurable Page Size**: Set initial page size via props
2. **Runtime Changes**: Users can change page size through the table pagination controls
3. **Automatic Data Loading**: When page size increases and more data is needed, it's automatically loaded
4. **State Synchronization**: Page size and current page are synchronized between DataTable2 and LazyDataSource
5. **Callback Support**: Get notified when page or page size changes via `onPageChange` callback

## Migration from DataTable

Key differences from the original DataTable:

1. **No rowKey prop**: Uses `controller.getId(item)` automatically
2. **No search functionality**: Will be implemented in LazyDataSourceSearch
3. **New mutation system**: Replace `mutateItems` with the new mutation operations
4. **LazyDataSource**: Replace direct controller usage with LazyDataSource

## Benefits

1. **Better Performance**: Only loads data as needed
2. **Memory Efficient**: Doesn't load all data at once
3. **Flexible Mutations**: Three different strategies for updating data
4. **Smart Pagination**: Automatically handles loading for pagination
5. **Consistent State**: Better state management through LazyDataSource
