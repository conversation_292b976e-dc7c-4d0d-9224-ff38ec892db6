import { Fetcher2 } from "@/functions/fetcher2";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export interface AgentAlias extends IBasemodel {
    agentId: string;
    alias: string;
    description: string;
    agentTag: string;
    agentName: string;
}

export enum AgentAliasActions {
    EDIT,
    DELETE,
    SWAP,
    CREATE
}

export interface AgentAliasCreateRequest {
    alias: string;
    description: string;
    agentTag: string;
}

export class AgentAliasController implements IBasemodelController<AgentAlias, AgentAliasActions> {
    getId: (item: AgentAlias) => string = (item) => item.alias;
    useGet = (ids: string[], options?: SWRConfiguration) => Fetcher2.SWRMultiTemplate<AgentAlias>(ids.map((id) => APIRoutes.AgentAliasController.GET.replace("{aliasId}", id)), { method: 'GET' }, options)
    can = (action: AgentAliasActions, onItem: AgentAlias) => {
        return true;
    }
    useList = (request: ListRequest) => Fetcher2.SWRTemplate<ListResponse<AgentAlias>>(APIRoutes.AgentAliasController.LIST, {method: 'GET', queryString: request})
    
    useDelete = (id: string) => Fetcher2.SWRMutationTemplate<AgentAlias>(APIRoutes.AgentAliasController.DELETE, {method: 'DELETE', urlPlaceholders: {agentId: id}})
    
    useSwap = (id: string) => Fetcher2.SWRMutationTemplate<AgentAlias>(APIRoutes.AgentAliasController.SWAP, {method: 'POST', urlPlaceholders: {agentId: id}})

    useCreate = (id: string) => Fetcher2.SWRMutationTemplate<AgentAlias>(APIRoutes.AgentAliasController.CREATE, {method: 'POST', urlPlaceholders: {agentId: id}})
}

