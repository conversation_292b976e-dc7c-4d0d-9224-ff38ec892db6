import { KnowledgeBase, KnowledgebaseActions, KnowledgebaseController, KnowledgeBaseStatus } from "@/models/knowledgebase";
import { Button, Dropdown, MenuProps, Space, Spin, TableColumnsType, theme } from "antd";
import {
    EditOutlined,
    RocketOutlined,
    DeleteOutlined,
    CloudUploadOutlined,
    <PERSON><PERSON><PERSON>Outlined,
    LoadingOutlined,
} from '@ant-design/icons';
import { GetKnowledgebaseStatusTag } from "@/components/knowledgebase/knowledgebase-description";
import { useTimeAgo } from "next-timeago";
import DataTable from "@/components/data-table";
import React, { isValidElement } from "react";


interface KnowledgebaseListParams{
    onClick: (record : KnowledgeBase) => void;
    onEdit: (record : KnowledgeBase) => void;
    onUploadData: (record : KnowledgeBase) => void;
    onDeploy: (record : KnowledgeBase) => void;
    onDelete: (record : KnowledgeBase) => void;
    mutateObjects? : KnowledgeBase[];
}



const KnowledgebaseList: React.FC<KnowledgebaseListParams> = (params: KnowledgebaseListParams) => {
    const knowledgebaseController : KnowledgebaseController = new KnowledgebaseController();
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState(false);
    
    const {
        token: { colorError },
    } = theme.useToken();
    const { TimeAgo } = useTimeAgo();

    const contextMenuItems = (record : KnowledgeBase) : MenuProps['items'] => ([
        {
            label: 'Edit',
            key: '1',
            icon: <EditOutlined />,
            onClick: () => { if(params.onEdit) params.onEdit(record); },
            disabled: !knowledgebaseController.can(KnowledgebaseActions.EDIT, record)
        },
        {
            label: 'Deploy',
            key: '2',
            icon: <RocketOutlined />,
            onClick: () => { if(params.onDeploy) params.onDeploy(record); },
        },
        {
            label: 'Delete',
            key: '3',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => { if(params.onDelete) params.onDelete(record); },
            disabled: !knowledgebaseController.can(KnowledgebaseActions.DELETE, record)
        },
      ]);
      
      const contextMenuProps = (record:KnowledgeBase) => ({
        items: contextMenuItems(record)
      });

      const columns: TableColumnsType<KnowledgeBase> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Name', dataIndex: 'name', render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{value}</a>;
            }
        },
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'Status', dataIndex: 'status', render: (status: KnowledgeBaseStatus, record: KnowledgeBase, index) => {
                const text = GetKnowledgebaseStatusTag(status);
                return (
                    <Space size={'small'}>
                        {text}
                        <Spin key={record.kbId} indicator={<LoadingOutlined spin />} spinning={pendingData.includes(record.kbId) && isLoadingData} />
                    </Space>
                );
            },
            shouldCellUpdate: (record: KnowledgeBase, prevRecord: KnowledgeBase) => {
                if (record === undefined || prevRecord === undefined) return false;
                return (record.status != KnowledgeBaseStatus.READY || (pendingData.includes(record.kbId) && isLoadingData) || record.status != prevRecord.status)
            },
            width: 160,
        },
        { title: 'Tag', dataIndex: 'tag' },
        {
            title: 'Last Modified', dataIndex: 'lastChangeTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<KnowledgeBase, KnowledgebaseController>
                controller={new KnowledgebaseController()}
                itemUpdateInterval={10000}
                tableColumns={columns}
                onItemsValidatingChange={(isValidating : boolean, validatingIdList : string[]) => {
                    setPendingData(validatingIdList);
                    setIsLoadingData(isValidating);
                }}
                shouldInvalidate={(entry : KnowledgeBase) => entry.status != KnowledgeBaseStatus.READY}
                mutateItems={params.mutateObjects}
                rowKey="kbId"
              />
    )


}

export default KnowledgebaseList;