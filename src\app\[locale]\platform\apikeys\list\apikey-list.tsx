import { Button, Dropdown, MenuProps, Space, TableColumnsType } from "antd";
import {
    RocketOutlined,
    DeleteOutlined,
    EllipsisOutlined,
    PlayCircleOutlined,
    PauseCircleOutlined,
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";
import DataTable from "@/components/data-table";
import React from "react";
import { ApiKey, ApiKeyActions, ApiKeyController } from "@/models/apikey";
import ApiKeyStatus from "@/components/apikey/apikey-status";


interface ApiKeyListParams {
    onClick: (record: ApiKey) => void;
    onPause: (record: ApiKey, pause: boolean) => void;
    onDelete: (record: ApiKey) => void;
    mutateObjects?: ApiKey[];
}



const ApiKeyList: React.FC<ApiKeyListParams> = (params: ApiKeyListParams) => {
    const apiKeyController: ApiKeyController = new ApiKeyController();
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState(false);
    const { TimeAgo } = useTimeAgo();

    const contextMenuItems = (record: ApiKey): MenuProps['items'] => ([
        {
            label: (!record.paused)?'Pause':'Resume',
            key: 'pause',
            icon: (!record.paused)?<PauseCircleOutlined />:<PlayCircleOutlined />,
            onClick: () => { if (params.onPause) params.onPause(record, record.paused); },
            disabled: (!record.paused)? !apiKeyController.can(ApiKeyActions.PAUSE, record) : !apiKeyController.can(ApiKeyActions.RESUME, record)
        },
        {
            label: 'Delete',
            key: 'delete',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => { if (params.onDelete) params.onDelete(record); },
            disabled: !apiKeyController.can(ApiKeyActions.DELETE, record)
        },
    ]);

    const contextMenuProps = (record: ApiKey) => ({
        items: contextMenuItems(record)
    });

    const columns: TableColumnsType<ApiKey> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Name', dataIndex: 'name', render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{value}</a>;
            }
        },
        { title: 'Key', dataIndex: 'key' },
        { title: 'Expire', dataIndex: 'expireAt' , render: ((value, record) => {
            return <TimeAgo date={value*1000} locale="en-US" live={true} />
        })},
        {
            title: 'Status', render: (_, record: ApiKey, index) => <ApiKeyStatus apiKey={record} />,
            width: 160,
        },
        {
            title: 'Visibility', dataIndex:'OwnerUserId', render: (value : string, record: ApiKey, index) => {
                if(value === undefined || value.length == 0) return 'Only You'
                return 'Account'
            },
            width: 160,
        },
        {
            title: 'Created', dataIndex: 'createdAt', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<ApiKey, ApiKeyController>
            controller={new ApiKeyController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: ApiKey) => false}
            mutateItems={params.mutateObjects}
            rowKey="key"
        />
    )
}

export default ApiKeyList;