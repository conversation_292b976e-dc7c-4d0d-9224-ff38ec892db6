import { IBasemodel, IBasemodelController } from "@/models/base-model";
import { Modal } from "antd";
import React, { PropsWithChildren } from "react";

interface ConfirmDeleteProps<T extends IBasemodel> {
    open: boolean;
    objectToDelete?: T;
    onDelete: (obj: T) => void;
    onCancel: (obj: T) => void;
    controller: IBasemodelController<T, any>;
    title?: string;
    description?: string;
}

function ConfirmDeleteModal<T extends IBasemodel>(props: ConfirmDeleteProps<T>) {
    const { data, error, trigger, isMutating } = props.controller.useDelete((props.objectToDelete) ? props.controller.getId(props.objectToDelete) : '')

    React.useEffect(() => {
        if (data) props.onDelete(data)
    }, [data])

    return (
        <Modal
            zIndex={9999}
            title={(props.title) ? props.title : "Are you sure to delete?"}
            open={props.open}
            onOk={() => {
                trigger();
            }}
            confirmLoading={isMutating}
            onCancel={(e) => props.onCancel(props.objectToDelete ?? data)}
        >
            <p>{(props.description) ? props.description : "This action is irreversible."}</p>
        </Modal>
    );
}

export default ConfirmDeleteModal;