import { BaseNodeData, BaseNodeFormElement, BaseNodeProps } from "./node/base-node";

export interface NodeComponentInformation<T> {
    node: React.ComponentType<BaseNodeProps<T>>;
    description: React.ComponentType<BaseNodeData<T>>;
    form: React.ComponentType<BaseNodeFormElement<T>>;
    initializer: () => T;
    icon: React.ReactNode;
    name: string;
}

export type NodeTypes = {
    [key: string]: NodeComponentInformation<any>;
}