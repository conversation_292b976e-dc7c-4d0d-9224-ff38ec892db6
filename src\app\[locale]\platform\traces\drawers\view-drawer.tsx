import { Trace, TraceStatus } from "@/models/trace";
import { Button, Descriptions, Space, Tag, Typography } from "antd";
import React from "react";
import {
    PlayCircleOutlined,
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";
import { useRouter } from "next/navigation";

interface TraceViewDrawerProps {
    trace: Trace;
    isOpen: boolean;
    onClose: () => void;
}

const TraceViewDrawer: React.FC<TraceViewDrawerProps> = ({ trace, isOpen, onClose }) => {
    const router = useRouter();

    const getStatusColor = (status: TraceStatus) => {
        switch (status) {
            case TraceStatus.RUNNING:
                return 'processing';
            case TraceStatus.COMPLETED:
                return 'success';
            case TraceStatus.FAILED:
                return 'error';
            case TraceStatus.CANCELLED:
                return 'default';
            default:
                return 'default';
        }
    };

    const handleOpenTraceViewer = () => {
        router.push(`/platform/traces/${trace.id}/viewer`);
        onClose();
    };

    return (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Typography.Title level={4}>Trace Information</Typography.Title>
            
            <Descriptions bordered column={1}>
                <Descriptions.Item label="Trace ID">
                    {trace.id}
                </Descriptions.Item>
                <Descriptions.Item label="Account ID">
                    {trace.accountId}
                </Descriptions.Item>
                <Descriptions.Item label="Session ID">
                    {trace.sessionId}
                </Descriptions.Item>
                <Descriptions.Item label="Flow">
                    {trace.flowName || 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                    <Tag color={getStatusColor(trace.status)}>
                        {trace.status}
                    </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Trace Time">
                    <TimeAgo date={trace.traceTime * 1000} locale="en-US" live={true} />
                </Descriptions.Item>
                <Descriptions.Item label="Created At">
                    <TimeAgo date={trace.createdAt * 1000} locale="en-US" live={true} />
                </Descriptions.Item>
                <Descriptions.Item label="Last Modified">
                    <TimeAgo date={trace.lastChangeTimestamp * 1000} locale="en-US" live={true} />
                </Descriptions.Item>
            </Descriptions>

            <Space>
                <Button 
                    type="primary" 
                    icon={<PlayCircleOutlined />}
                    onClick={handleOpenTraceViewer}
                >
                    Open Trace Viewer
                </Button>
                <Button onClick={onClose}>
                    Close
                </Button>
            </Space>
        </Space>
    );
};

export default TraceViewDrawer;
