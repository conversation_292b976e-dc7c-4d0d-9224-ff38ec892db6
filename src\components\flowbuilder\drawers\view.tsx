import ConditionDescription from "@/components/flowbuilder/conditions/condition-description";
import { BaseNode, BaseNodeData, NodeEvents } from "@/components/behaviortree/node/base-node";
import { <PERSON><PERSON>, Divider, Flex, Form, Input, Space } from "antd";
import { Field, OptionGroup, RuleGroupType } from "react-querybuilder";
import { EditOutlined, FilterOutlined } from '@ant-design/icons';
import EditableTitle from "@/components/editable-title";
import React from "react";

export type ViewNodeDrawerProps<T> = {
    node: BaseNodeData<T>;
    nodeDescription: React.JSX.Element;
    treeFields: OptionGroup<Field>[];
    onEditConditions: (data: BaseNodeData<T>) => void;
    onEditProperties: (data: BaseNodeData<T>) => void;
    onChangeTitle: (newTitle : string) => void;
    nodes : string[];
    nodes2 : string[];
    getComponent : () => React.ReactNode;
}

function ViewNodeDrawer<T>(props: ViewNodeDrawerProps<T>) {
    React.useEffect(() => {
        console.log("ViewNodeDrawer nodes:")
        console.log(props.nodes);
    }, [props.nodes])

    React.useEffect(() => {
        console.log("ViewNodeDrawer nodes2:")
        console.log(props.nodes2);
    }, [props.nodes2])

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            {props.getComponent()}
            <EditableTitle title={props.node.name} level={3} onTitleChange={props.onChangeTitle} />
            <Divider style={{ color: '#ccc' }}>Properties</Divider>
            {props.nodeDescription}
            <Flex justify='center' align='center' gap="middle">
                <Button type="default" icon={<EditOutlined />} onClick={() => { props.onEditProperties(props.node) }}>Change Properties</Button>
            </Flex>
            <Divider style={{ color: '#ccc' }}>Rules</Divider>
            <ConditionDescription contitionData={props.node.nodeConditions} fields={props.treeFields} />
            <Flex justify='center' align='center' gap="middle">
                <Button type="default" icon={<FilterOutlined />} onClick={() => { props.onEditConditions(props.node) }}>Change Conditions</Button>
            </Flex>
        </Space>
    )
}


export default ViewNodeDrawer;