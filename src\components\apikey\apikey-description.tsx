import { <PERSON><PERSON><PERSON><PERSON> } from "@/models/apikey"
import { Descriptions, DescriptionsProps, Skeleton } from "antd";
import { useTimeAgo } from "next-timeago";
import ApiKeyStatus from "./apikey-status";

export interface ApiKeyDescriptionParams {
    locale?: string,
    apiKey?: ApiKey,
    layout?: 'vertical' | 'horizontal';
}

const ApiKeyDescription: React.FC<ApiKeyDescriptionParams> = (params) => {
    const { TimeAgo } = useTimeAgo();

    const ApiKeyDescriptionFields: DescriptionsProps['items'] = (params.apiKey) ? [
        {
            key: 'name',
            label: 'Name',
            children: params.apiKey.name,
        },
        {
            key: 'key',
            label: 'Key',
            children: params.apiKey.key,
            span: 2,
        },
        {
            key: 'Status',
            label: 'Status',
            dataIndex: 'status',
            children: <ApiKeyStatus apiKey={params.apiKey} />,
        },
        {
            key: 'expiredAt',
            label: 'Expire At',
            children: (<TimeAgo date={params.apiKey.expireAt * 1000} locale="en-US" live={true} />),
        },
        {
            key: 'Created At',
            label: 'Created At',
            children: (<TimeAgo date={params.apiKey.createdAt * 1000} locale="en-US" live={true} />),
        },
    ].filter(item => item.children !== undefined) : [];



    return ((params.apiKey) ? <Descriptions items={ApiKeyDescriptionFields} layout={params.layout ?? 'horizontal'}></Descriptions> : <Skeleton />)
}

export default ApiKeyDescription;