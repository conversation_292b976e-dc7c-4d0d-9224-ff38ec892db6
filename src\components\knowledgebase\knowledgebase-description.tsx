import { Descriptions, DescriptionsProps, Skeleton, Tag } from "antd";
import {
    SyncOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";
import { KnowledgeBaseStatus, KnowledgeBase } from "@/models/knowledgebase";

export interface KnowledgebaseDescriptionParams {
    children?: React.JSX.Element;
    locale?: string,
    knowledgebase?: KnowledgeBase,
    layout?: 'vertical' | 'horizontal'
}

export function GetKnowledgebaseStatusTag(status: KnowledgeBaseStatus) {
    switch (status) {
        case KnowledgeBaseStatus.QUEUED:
            return (<Tag icon={<ClockCircleOutlined />} color="default">waiting</Tag>)
        case KnowledgeBaseStatus.DATASOURCE_CREATED:
        case KnowledgeBaseStatus.DB_ENTRY_CREATED:
        case KnowledgeBaseStatus.KB_CREATED:
            return (<Tag icon={<SyncOutlined spin />} color="processing">creating</Tag>)
        case KnowledgeBaseStatus.READY:
            return (<Tag icon={<CheckCircleOutlined />} color="success">ready</Tag>)
        case KnowledgeBaseStatus.DEPLOYING:
        case KnowledgeBaseStatus.SEALING:
        case KnowledgeBaseStatus.SYNCRONIZING:
        case KnowledgeBaseStatus.TAGGING:
            return (<Tag icon={<SyncOutlined spin />} color="purple">deploying</Tag>)
        default:
            return (<Tag color="default">unknown</Tag>)

    }
}

const KnowledgebaseDescription: React.FC<KnowledgebaseDescriptionParams> = (params) => {
    const { TimeAgo } = useTimeAgo();

    const KnowledgebaseDescriptionFields: DescriptionsProps['items'] = (params.knowledgebase) ? [
        {
            key: 'Name',
            label: 'Name',
            children: params.knowledgebase.name,
        },
        {
            key: 'Description',
            label: 'Description',
            span: 2,
            children: params.knowledgebase.description,
        },
        {
            key: 'Status',
            label: 'Status',
            children: GetKnowledgebaseStatusTag(params.knowledgebase.status),
        },
        {
            key: 'Tag',
            label: 'Tag',
            children: params.knowledgebase.tag,
        },
        {
            key: 'Last Modified',
            label: 'Last Modified',
            children: (<TimeAgo date={params.knowledgebase.lastChangeTimestamp * 1000} locale="en-US" live={true} />),
        },
        {
            key: 'Created At',
            label: 'Created At',
            children: (new Date(params.knowledgebase.createdAt * 1000)).toLocaleDateString(params.locale),
        },
    ].filter(item => item.children !== undefined) : [];

    return ((params.knowledgebase) ? <Descriptions items={KnowledgebaseDescriptionFields} layout={params.layout ?? 'horizontal'}></Descriptions> : <Skeleton />)
}

export default KnowledgebaseDescription;