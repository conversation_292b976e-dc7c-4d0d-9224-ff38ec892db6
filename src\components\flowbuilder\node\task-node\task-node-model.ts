import { BaseNode } from '../base-node';
import { BaseTaskProps } from './base-task';

export type TaskNodeModelData = {
    tasks: BaseTaskProps<any>[];
}

export type TaskNodeModel = BaseNode<TaskNodeModelData>;

// Dummy task type for demonstration
export type DummyTaskData = {
    message: string;
}

export function InitializeTaskNodeModelData() : TaskNodeModelData {
    return {
        tasks: []
    }
}