'use client'

import React from 'react';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Checkbox, Form, Input, Flex, Card, Spin, Alert, Result } from 'antd';
import { useTranslation } from 'react-i18next';
import { LoginFunctions } from '@/functions/login';
import { useRouter, usePathname } from 'next/navigation';
import AuthenticationGate from '@/components/auth-gate';
import '@ant-design/v5-patch-for-react-19';

const boxStyle: React.CSSProperties = {
    width: '100%',
    paddingTop: 200,
};

const App: React.FC = () => {
    const { t } = useTranslation('login');

    const onFinish = (values: any) => {
        if (loading) return;
        setLoading(true);

        LoginFunctions.RequestLogin(values.email, values.password, values.remember)
            .then((resp: LoginFunctions.RequestLoginResponse) => {
                console.log(resp);
                if (resp.success) {
                    window.location.href = '/platform/home';
                } else {
                    setLoading(false);
                    setErrorState({ visible: true, message: resp.errorMessage ?? "", title: resp.errorTitle ?? "" });
                }
            });
    };

    const handleClose = () => {
        setErrorState({ visible: false, message: '', title: '' });
    };

    const [passwordVisible, setPasswordVisible] = React.useState(false);
    const [loading, setLoading] = React.useState<boolean>(false);
    const [errorState, setErrorState] = React.useState({ visible: false, message: '', title: '' });
    const router = useRouter();

    return (
        <AuthenticationGate requireLogIn={false}>
            <Flex style={boxStyle} justify='center' align='center'>
                <Spin spinning={loading} delay={0}>
                    <Card cover={<img alt="coral agents" src="/images/logo.png" />} style={{ width: 400 }}>
                        <Form
                            name="login"
                            initialValues={{ remember: true }}
                            style={{ maxWidth: 400 }}
                            onFinish={onFinish}
                        >

                            <Form.Item
                                name="email"
                                rules={[{ required: true, message: 'Please input your Email!' }]}
                            >
                                <Input prefix={<UserOutlined />} placeholder={t("email")} />
                            </Form.Item>
                            <Form.Item
                                name="password"
                                rules={[{ required: true, message: 'Please input your Password!' }]}
                            >
                                <Input.Password
                                    prefix={<LockOutlined />}
                                    placeholder={t("password")}
                                    visibilityToggle={{ visible: passwordVisible, onVisibleChange: setPasswordVisible }}
                                />
                            </Form.Item>
                            <Form.Item>
                                <Flex justify="space-between" align="center">
                                    <Form.Item name="remember" valuePropName="checked" noStyle>
                                        <Checkbox>Remember me</Checkbox>
                                    </Form.Item>
                                    <a href="">Forgot password</a>
                                </Flex>
                            </Form.Item>

                            <Form.Item>
                                <Button block type="primary" htmlType="submit">
                                    {t("login")}
                                </Button>
                                or <a href="">Register now!</a>
                            </Form.Item>

                        </Form>

                        {errorState.visible && (<Alert
                            message={errorState.title}
                            description={errorState.message}
                            type="error"
                            closable afterClose={handleClose}
                            showIcon
                        />)}
                    </Card>
                </Spin>
            </Flex>
        </AuthenticationGate>)
};

export default App;