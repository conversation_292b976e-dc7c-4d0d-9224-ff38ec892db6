import ApiKeyDescription from "@/components/apikey/apikey-description";
import { Api<PERSON><PERSON>, <PERSON>pi<PERSON><PERSON><PERSON><PERSON>, ApiKeyController } from "@/models/apikey";
import { Button, Divider, Flex, Skeleton, Space } from "antd";
import {
    DeleteOutlined,
    PauseCircleOutlined,
    PlayCircleOutlined,
} from '@ant-design/icons';

interface ApiKeyCreateDrawerParams {
    onPause: (record: Api<PERSON><PERSON>, pause: boolean) => void;
    onDelete: (record: ApiKey) => void;
    data?: ApiKey;
}

const ApiKeyViewDrawer: React.FC<ApiKeyCreateDrawerParams> = (params: ApiKeyCreateDrawerParams) => {
    const apiKeyController: ApiKeyController = new ApiKeyController();

    if (
        params.data === undefined
    ) return (<Skeleton />)

    return (
        <Space direction="vertical" style={{ padding: '0 24px' }}>
            <ApiKeyDescription layout="vertical" apiKey={params.data} />
            <Divider style={{ color: '#ccc' }}>Actions</Divider>
            <Flex justify='center' align='center' gap="middle">
                {(params.data.paused) ? (
                    <Button disabled={!apiKeyController.can(ApiKeyActions.PAUSE, params.data)} type="text" icon={<PlayCircleOutlined />} onClick={() => { params.onPause(params.data, false) }}>Resume</Button>
                ) : (
                    <Button disabled={!apiKeyController.can(ApiKeyActions.PAUSE, params.data)} type="text" icon={<PauseCircleOutlined />} onClick={() => { params.onPause(params.data, true) }}>Pause</Button>
                )}
                <Button disabled={!apiKeyController.can(ApiKeyActions.DELETE, params.data)} type="primary" danger icon={<DeleteOutlined />} onClick={() => { params.onDelete(params.data) }}>Delete</Button>
            </Flex>
        </Space>
    )
}

export default ApiKeyViewDrawer;