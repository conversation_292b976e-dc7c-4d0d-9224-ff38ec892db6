import { KnowledgeBaseFileUploadStatus } from "@/models/knowledgebasefile"
import { Tag } from "antd"
import {CheckCircleOutlined, ClockCircleOutlined, SyncOutlined, ExceptionOutlined} from '@ant-design/icons'

export function GetKnowledgebaseFileStatusTag(status: KnowledgeBaseFileUploadStatus) {
    switch (status) {
        case KnowledgeBaseFileUploadStatus.QUEUED:
            return (<Tag icon={<ClockCircleOutlined />} color="default">waiting</Tag>)
        case KnowledgeBaseFileUploadStatus.FAILED:
            return (<Tag icon={<ExceptionOutlined />} color="red">failed</Tag>)
        case KnowledgeBaseFileUploadStatus.READY:
            return (<Tag icon={<CheckCircleOutlined />} color="success">ready</Tag>)
        case KnowledgeBaseFileUploadStatus.UPLOADING:
            return (<Tag icon={<SyncOutlined spin />} color="purple">uploading</Tag>)
        default:
            return (<Tag color="default">unknown</Tag>)
    }
}