import { Form, InputNumber, Space } from "antd";
import { BaseNodeFormElement } from "../base-node";
import { ParallelNodeModelData } from "./parallel-node-model";
import React from "react";
import SubmitButton from "@/components/submit-button";

export interface ParallelNodeFormProps extends BaseNodeFormElement<ParallelNodeModelData> {
}

function submitForm(form: any): ParallelNodeModelData {
    const values = form.getFieldsValue();
    return {
        outputCount: values.outputCount || 2
    };
}

export default function ParallelNodeForm(props: ParallelNodeFormProps) {
    const [form] = Form.useForm<ParallelNodeModelData>();

    return (
        <Form 
            form={form} 
            layout="vertical" 
            onFinish={() => {props.onChange(submitForm(form)); props.onCancel()}}
            initialValues={props.data}
        >
            <Form.Item 
                label="Number of Outputs" 
                name="outputCount"
                rules={[
                    { required: true, message: 'Please enter number of outputs' },
                    { type: 'number', min: 1, max: 10, message: 'Must be between 1 and 10' }
                ]}
            >
                <InputNumber 
                    min={1} 
                    max={10} 
                    placeholder="Number of outputs"
                    style={{ width: '100%' }}
                />
            </Form.Item>
            <Space>
                <SubmitButton form={form}>Save</SubmitButton>
            </Space>
        </Form>
    );
}
