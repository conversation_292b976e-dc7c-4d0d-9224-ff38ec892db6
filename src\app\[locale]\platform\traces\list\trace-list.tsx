import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>er, TraceStatus } from "@/models/trace";
import { Button, Dropdown, Space, Tag } from "antd";
import type { TableColumnsType } from 'antd';
import React from "react";
import {
    EllipsisOutlined,
    EyeOutlined,
    PlayCircleOutlined,
} from '@ant-design/icons';
import DataTable from "@/components/data-table";
import { useTimeAgo } from "next-timeago";

interface TraceListProps {
    mutateObjects?: Trace[];
    onClick?: (record: Trace) => void;
    onOpenTraceViewer?: (record: Trace) => void;
}

const TraceList: React.FC<TraceListProps> = (params) => {
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState<boolean>(false);

    const contextMenuProps = (record: Trace) => ({
        items: [
            {
                key: 'view',
                label: 'View Details',
                icon: <EyeOutlined />,
                onClick: () => {
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }
            },
            {
                key: 'trace-viewer',
                label: 'Open Trace Viewer',
                icon: <PlayCircleOutlined />,
                onClick: () => {
                    if (params.onOpenTraceViewer) {
                        params.onOpenTraceViewer(record);
                    }
                }
            }
        ]
    });

    const getStatusColor = (status: TraceStatus) => {
        switch (status) {
            case TraceStatus.RUNNING:
                return 'processing';
            case TraceStatus.COMPLETED:
                return 'success';
            case TraceStatus.FAILED:
                return 'error';
            case TraceStatus.CANCELLED:
                return 'default';
            default:
                return 'default';
        }
    };

    const columns: TableColumnsType<Trace> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Trace ID', 
            dataIndex: 'id', 
            render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{value}</a>;
            }
        },
        {
            title: 'Session ID', 
            dataIndex: 'sessionId'
        },
        {
            title: 'Flow', 
            dataIndex: 'flowName',
            render: (value) => value || 'N/A'
        },
        {
            title: 'Status', 
            dataIndex: 'status',
            render: (status: TraceStatus) => (
                <Tag color={getStatusColor(status)}>
                    {status}
                </Tag>
            )
        },
        {
            title: 'Trace Time', 
            dataIndex: 'traceTime', 
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<Trace, TraceController>
            controller={new TraceController()}
            searchController={new TraceController()}
            enableSearch={true}
            searchPlaceholder="Search traces by ID, session ID, or flow name..."
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: Trace) => entry.status === TraceStatus.RUNNING}
            mutateItems={params.mutateObjects}
            rowKey="id"
        />
    )
}

export default TraceList;
