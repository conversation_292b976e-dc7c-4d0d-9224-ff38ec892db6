import { BaseTaskProps } from "../../behaviortree/node/task/base-task";
import { RuleGroupType } from "react-querybuilder";

export interface ConditionalClause {
    condition: RuleGroupType;
    tasks: BaseTaskProps<any>[];
}

export interface ConditionalTaskData {
    clauses: ConditionalClause[];
    elseTasks: BaseTaskProps<any>[];
}

export type ConditionalTaskProps = BaseTaskProps<ConditionalTaskData>;

export const createEmptyConditionalTask = (): ConditionalTaskData => ({
    clauses: [],
    elseTasks: []
});