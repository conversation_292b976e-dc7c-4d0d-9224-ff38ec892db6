import { IBasemodel, IBaseModelSearchController } from "@/models/base-model";
import { <PERSON>Com<PERSON><PERSON>, <PERSON><PERSON>, Card, Skeleton } from "antd";
import { DefaultOptionType } from "antd/es/select";
import React from "react";
import {
    LoadingOutlined,
} from '@ant-design/icons';

type SearchAndDescriptionItemProps<T extends IBasemodel> = {
    value?: string;
    searchResults: number;
    controller: IBaseModelSearchController<T>;
    placeholder?: string;
    searchIcon?: React.ReactNode;
    getDescription: (item: T) => React.ReactNode;
    onChange?: (itemId: string) => void
    style? : React.CSSProperties;
};


const SearchAndDescriptionItem = <T extends IBasemodel>(props: SearchAndDescriptionItemProps<T>) => {

    const [options, setOptions] = React.useState<DefaultOptionType[]>();
    const [selectedValue, setSelectedValue] = React.useState<T>();
    const [queryString, setQueryString] = React.useState<string>("")
    const { data, isValidating, isLoading } = props.controller.useSearch(queryString, props.searchResults);
    const [initalValue, setInitialValue] = React.useState<string>()
    const {
        data: itemData,
        isValidating: isValidatingItemData,
        isLoading: isLoadingItemData
    } = props.controller.useGet([initalValue ?? ''], { revalidateOnMount: (initalValue === undefined) })
    


    React.useEffect(() => {
        if(props.value !== undefined) setInitialValue(props.value);
    }, [])

    React.useEffect(() => {
        if (itemData === undefined) return;
        setSelectedValue(itemData[0]);
    }, [itemData])

    React.useEffect(() => {
        if (data === undefined || data.entries.length === 0) return;
        setOptions(data.entries.map(item => ({
            label: props.getDescription(item),
            value: props.controller.getId(item),
            item: item
        })))
    }, [data])

    if (initalValue !== undefined && (isValidatingItemData || isLoadingItemData))
        return <Skeleton style={props.style} />

    if (selectedValue !== undefined){
        return (
            <>
                <Card actions={[<Button onClick={()=> {
                        setSelectedValue(undefined);
                    }}>Change</Button>]}>
                    {props.getDescription(selectedValue)}
                    
                </Card>
            </>
        )
    }

    return (
        <AutoComplete
            style={props.style}
            prefix={(
                (!data && (isLoading || isValidating)) ?
                    <LoadingOutlined style={{ color: 'rgba(0,0,0,.25)' }} spin /> :
                    props.searchIcon ?? null
            )}
            placeholder={props.placeholder}
            options={options}
            onSelect={(value, option) => {
                setSelectedValue(option.item);
                if (props.onChange) props.onChange(value);
            }}
            onSearch={(text) => {
                if (text.length > 2) setQueryString(text);
            }}
        />
        
    )
}

export default SearchAndDescriptionItem;