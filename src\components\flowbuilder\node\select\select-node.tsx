'use client';

import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { <PERSON><PERSON>, Card, Flex, Space, Tag } from 'antd';
import React from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { SelectNodeModelData } from './select-node-model';
import { BaseNodeProps } from '../base-node';
import SelectNodeDescription from './select-node-description';

export const SelectNodeName = "select";

function SelectNode(props: BaseNodeProps<SelectNodeModelData>) {
    const outputCount = props.data.baseData.nodeData.outputCount || 2;
    
    return (
        <>
            <Card title={<Flex justify='stretch'>
                <div className='reactflow-wrapper'>
                    <Space>
                        {props.data.baseData.name}
                        <Tag color="default" bordered={false}>Select</Tag>
                    </Space>
                </div>
                <div>
                    <Button
                        style={{ width: '30px' }} type="default"
                        icon={<SettingOutlined />} size='small'
                        onClick={(event) => {
                            event.stopPropagation();
                            props.data.events?.onOpenDrawer();
                        }}
                    />
                </div>
            </Flex>}>
                <SelectNodeDescription {...props.data.baseData} />
            </Card>
            <Handle type="target" position={Position.Left} id="input" />
            {Array.from({ length: outputCount }, (_, index) => (
                <Handle 
                    key={`output-${index}`}
                    type="source" 
                    position={Position.Right} 
                    id={`output-${index}`}
                    style={{ top: `${((index + 1) * 100) / (outputCount + 1)}%` }}
                />
            ))}
        </>
    );
}

export default React.memo((props: BaseNodeProps<SelectNodeModelData>) => SelectNode(props));
