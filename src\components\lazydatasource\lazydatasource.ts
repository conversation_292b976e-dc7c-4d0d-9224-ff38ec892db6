import { IB<PERSON>model, IBasemodel<PERSON>ontroller, ListResponse } from "@/models/base-model";
import { ItemMutation } from "./lazymutation";

export enum ItemUpdateStrategy {
    MERGE = 'merge',
    REPLACE = 'replace'
}

// LazyDataSource interface for handling data loading and management
export interface LazyDataSource<T extends IBasemodel> {
    // Data access
    getData(): T[];
    getTotalCount(): number;
    getLoadedCount(): number;

    // Loading state
    isLoading(): boolean;
    isUpdatingItems(): boolean;

    // Data operations
    loadMore(): Promise<void>;
    loadUntilPage(page: number, pageSize: number): Promise<void>;
    loadAll(): Promise<void>;
    invalidate(): void;

    // Item mutations
    replaceItem(item: T): void;
    mergeItem(item: Partial<T>): void;
    deleteItem(id: string): void;
    applyMutations(mutations: ItemMutation<T>[]): void;

    // Item update management
    setShouldInvalidate(predicate: (item: T) => boolean): void;
    setItemUpdateInterval(interval: number): void;
    setItemUpdateStrategy(strategy: ItemUpdateStrategy): void;
    startItemUpdateCycle(): void;
    stopItemUpdateCycle(): void;
    getItemsToUpdate(): string[];
    updateItemFromSWR(itemId: string, data: T[] | undefined, error: any): void;
    markItemAsUpdating(itemId: string): void;
    completeUpdateCycle(): void;

    // Configuration
    setMinItemsPerRequest(count: number): void;
    setAutoLoadUntilExhaustion(enabled: boolean): void;
    setPageSize(pageSize: number): void;
    getPageSize(): number;
    getCurrentPage(): number;
    setCurrentPage(page: number): void;

    // Events
    onDataChange(callback: (data: T[]) => void): void;
    onLoadingChange(callback: (isLoading: boolean) => void): void;
    onItemsValidatingChange(callback: (isValidating: boolean, validatingIds: string[]) => void): void;
}