import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Segmented, <PERSON>, Tabs, theme } from 'antd';
import { DnDContextType, useDnDContext } from './dnd-context';
import { RobotOutlined, ReconciliationOutlined } from '@ant-design/icons';
import { SetAgentNodeName } from '@/components/behaviortree/node/setagent/set-agent-node';
import { Position } from '@xyflow/react';
import React from 'react';
import VariablesForm from './variables/variables-form';
import { FieldModel, FieldModelType } from './fields';
import { NodeTypes } from './types';

interface ComponentMenuProps{
  menuItems: NodeTypes;
  onChangeField : (values : FieldModel) => void;
  fields: FieldModel[];
  context: React.Context<DnDContextType<any>>;
}

interface FormatedField{
  title: string;
  description: string;
}

type FormatedFieldsObject = {[key in FieldModelType]: FormatedField[];}

const formatFields : (fields : FieldMode<PERSON>[]) => FormatedFieldsObject = (fields) => {
    return {
      variable: fields.filter(f => f.type == 'variable').map(f => ({"title": f.name, "description": `${f.dataType} - Initial Value: ${f.defaultValue ?? 'null'}`})),
      input: fields.filter(f => f.type == 'input').map(f => ({"title": f.name, "description": `${f.dataType} - Default Value: ${f.defaultValue ?? 'null'} - Required: ${f.required}`}))
    }
}

export default (props : ComponentMenuProps) => {
  const [_, setType] = useDnDContext(props.context);
  const [viewType, setViewType] = React.useState<FieldModelType>('input')
  const [showingList, setShowingList] = React.useState(true)
  const [fieldsData, setFieldsData] = React.useState(formatFields(props.fields))

  const {
    token: { colorPrimary },
  } = theme.useToken();

  const onDragStart = (event: React.DragEvent<HTMLDivElement>, nodeType: keyof typeof props.menuItems) => {
    setType(nodeType);
  };


  React.useEffect(() => {
    setFieldsData(formatFields(props.fields))
  }, [props.fields])

  return (
    <Tabs
      defaultActiveKey="1"
      type="card"
      size='middle'
      items={[
        {
          label: `Components`,
          key: 'Components',
          children: <List
            dataSource={Object.entries(props.menuItems)}
            style={{ padding: '15px' }}
            renderItem={(item) => (
              <List.Item>
                <div className="dndnode" onDragStart={(event) => onDragStart(event, item[0] as keyof typeof props.menuItems)} draggable>
                  <Space direction='horizontal'>
                    {item[1].icon}
                    {item[1].name}
                  </Space>
                </div>
              </List.Item>
            )}
          />,
        },
        {
          label: `Inputs & Variables`,
          key: 'Inputs',
          children: <Flex vertical style={{width:'100%'}} align='center'>
            <ConfigProvider
              theme={{
                components: {
                  Segmented: {
                    itemSelectedColor: '#fff',
                    itemSelectedBg: colorPrimary,
                  },
                },
              }}
            >
            <Segmented options={['input', 'variable']} onChange={setViewType} value={viewType} style={{margin:'10px'}} />
            </ConfigProvider>
            <VariablesForm formType={viewType} onClose={() => { setShowingList(true); }} onShow={() => { setShowingList(false) }} onSave={(values) => {
              props.onChangeField(values)
             }}
             fields={props.fields}

             />
            {showingList ? (<>
            <Divider style={{color:'#ccc'}}>{viewType=='input' ? "Inputs" : "Variables"}</Divider>
            <List
              style={{width:'100%', margin:'10px'}}
              pagination={{ position: 'bottom', align: 'center', pageSize:5 }}
              dataSource={fieldsData[viewType]}
              renderItem={(item, index) => (
                <List.Item>
                  <List.Item.Meta
                    title={item.title}
                    description={item.description}
                  />
                </List.Item>
              )}
            /></>) : null}</Flex>,
        }
      ]}
    />

  );
};