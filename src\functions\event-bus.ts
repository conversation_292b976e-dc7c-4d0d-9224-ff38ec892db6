export type EventBusCallbackFunction<T> = (data : T) => void;

const EventBus = {
    on<T>(event : string, callback : EventBusCallbackFunction<T>) {
      document.addEventListener(event, ((e : CustomEvent<T>) => callback(e.detail)) as EventListener);
    },
    dispatch<T>(event : string, data : T) {
      document.dispatchEvent(new CustomEvent(event, { detail: data }));
    },
    remove<T>(event : string, callback : EventBusCallbackFunction<T>) {
      document.removeEventListener(event, ((e : CustomEvent<T>) => callback(e.detail)) as EventListener);
    },
  };
  
  export default EventBus;