import { Form, InputNumber, Space } from "antd";
import { BaseNodeFormElement } from "../base-node";
import { SequenceNodeModelData } from "./sequence-node-model";
import React from "react";
import SubmitButton from "@/components/submit-button";

export interface SequenceNodeFormProps extends BaseNodeFormElement<SequenceNodeModelData> {
}

function submitForm(form: any): SequenceNodeModelData {
    const values = form.getFieldsValue();
    return {
        outputCount: values.outputCount || 2
    };
}

export default function SequenceNodeForm(props: SequenceNodeFormProps) {
    const [form] = Form.useForm<SequenceNodeModelData>();

    return (
        <Form 
            form={form} 
            layout="vertical" 
            onFinish={() => {props.onChange(submitForm(form)); props.onCancel()}}
            initialValues={props.data}
        >
            <Form.Item 
                label="Number of Outputs" 
                name="outputCount"
                rules={[
                    { required: true, message: 'Please enter number of outputs' },
                    { type: 'number', min: 1, max: 10, message: 'Must be between 1 and 10' }
                ]}
            >
                <InputNumber 
                    min={1} 
                    max={10} 
                    placeholder="Number of outputs"
                    style={{ width: '100%' }}
                />
            </Form.Item>
            <Space>
                <SubmitButton form={form}>Save</SubmitButton>
            </Space>
        </Form>
    );
}
