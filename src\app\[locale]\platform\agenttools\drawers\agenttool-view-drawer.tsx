import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>roller, <PERSON><PERSON><PERSON><PERSON>anguage, AgentToolStatus } from "@/models/agenttool";
import { <PERSON>ert, Button, Card, Descriptions, Divider, Space, Tag, Typography, message } from "antd";
import {
    EditOutlined,
    RocketOutlined,
    DeleteOutlined,
    CheckCircleOutlined,
    CopyOutlined,
    CodeOutlined,
    ClockCircleOutlined,
    Exclamation<PERSON>ircleOutlined
} from '@ant-design/icons';
import React from "react";
import { useTimeAgo } from "next-timeago";
import { CodeBlock, dracula } from 'react-code-blocks';

interface AgentToolViewDrawerParams {
    agentTool: AgentTool;
    onEdit: (record: AgentTool) => void;
    onDelete: (record: AgentTool) => void;
    onDeploy: (record: AgentTool) => void;
    onValidate: (record: AgentTool) => void;
    onDuplicate: (record: AgentTool) => void;
}

const AgentToolViewDrawer: React.FC<AgentToolViewDrawerParams> = (params: AgentToolViewDrawerParams) => {
    const controller = new AgentToolController();
    const { data: deployData, trigger: deployTrigger, isMutating: isDeploying } = controller.useDeploy(params.agentTool.id);
    const { data: validateData, trigger: validateTrigger, isMutating: isValidating } = controller.useValidate(params.agentTool.id);
    const { data: duplicateData, trigger: duplicateTrigger, isMutating: isDuplicating } = controller.useDuplicate(params.agentTool.id);
    const { TimeAgo } = useTimeAgo();

    React.useEffect(() => {
        if (deployData) {
            message.success('Tool deployed successfully');
            params.onDeploy(deployData);
        }
    }, [deployData]);

    React.useEffect(() => {
        if (validateData) {
            message.success('Tool validated successfully');
            params.onValidate(validateData);
        }
    }, [validateData]);

    React.useEffect(() => {
        if (duplicateData) {
            message.success('Tool duplicated successfully');
            params.onDuplicate(duplicateData);
        }
    }, [duplicateData]);

    const getLanguageColor = (language: AgentToolLanguage): string => {
        switch (language) {
            case AgentToolLanguage.JAVA:
                return 'orange';
            case AgentToolLanguage.CSHARP:
                return 'purple';
            case AgentToolLanguage.PYTHON:
                return 'green';
            default:
                return 'default';
        }
    };

    const getStatusColor = (status: AgentToolStatus): string => {
        switch (status) {
            case AgentToolStatus.DRAFT:
                return 'default';
            case AgentToolStatus.VALIDATING:
                return 'processing';
            case AgentToolStatus.READY:
                return 'success';
            case AgentToolStatus.FAILED:
                return 'error';
            case AgentToolStatus.DEPLOYING:
                return 'processing';
            case AgentToolStatus.DEPLOYED:
                return 'cyan';
            default:
                return 'default';
        }
    };

    const getStatusIcon = (status: AgentToolStatus) => {
        switch (status) {
            case AgentToolStatus.VALIDATING:
            case AgentToolStatus.DEPLOYING:
                return <ClockCircleOutlined spin />;
            case AgentToolStatus.FAILED:
                return <ExclamationCircleOutlined />;
            case AgentToolStatus.READY:
            case AgentToolStatus.DEPLOYED:
                return <CheckCircleOutlined />;
            default:
                return null;
        }
    };

    const getLanguageForCodeBlock = (language: AgentToolLanguage): string => {
        switch (language) {
            case AgentToolLanguage.PYTHON:
                return 'python';
            case AgentToolLanguage.JAVA:
                return 'java';
            case AgentToolLanguage.CSHARP:
                return 'csharp';
            default:
                return 'text';
        }
    };

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            {/* Action Buttons */}
            <Space wrap>
                <Button
                    type="primary"
                    icon={<EditOutlined />}
                    onClick={() => params.onEdit(params.agentTool)}
                    disabled={!controller.can(AgentToolActions.EDIT, params.agentTool)}
                >
                    Edit
                </Button>
                <Button
                    icon={<CheckCircleOutlined />}
                    onClick={() => validateTrigger()}
                    loading={isValidating}
                    disabled={!controller.can(AgentToolActions.VALIDATE, params.agentTool)}
                >
                    Validate
                </Button>
                <Button
                    icon={<RocketOutlined />}
                    onClick={() => deployTrigger()}
                    loading={isDeploying}
                    disabled={!controller.can(AgentToolActions.DEPLOY, params.agentTool)}
                >
                    Deploy
                </Button>
                <Button
                    icon={<CopyOutlined />}
                    onClick={() => duplicateTrigger()}
                    loading={isDuplicating}
                    disabled={!controller.can(AgentToolActions.DUPLICATE, params.agentTool)}
                >
                    Duplicate
                </Button>
                <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => params.onDelete(params.agentTool)}
                    disabled={!controller.can(AgentToolActions.DELETE, params.agentTool)}
                >
                    Delete
                </Button>
            </Space>

            {/* Error Message */}
            {params.agentTool.status === AgentToolStatus.FAILED && params.agentTool.errorMessage && (
                <Alert
                    message="Validation Failed"
                    description={params.agentTool.errorMessage}
                    type="error"
                    showIcon
                />
            )}

            {/* Tool Information */}
            <Card title="Tool Information">
                <Descriptions column={2} bordered>
                    <Descriptions.Item label="Name">{params.agentTool.name}</Descriptions.Item>
                    <Descriptions.Item label="Status">
                        <Tag color={getStatusColor(params.agentTool.status)} icon={getStatusIcon(params.agentTool.status)}>
                            {params.agentTool.status}
                        </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="Language">
                        <Tag color={getLanguageColor(params.agentTool.language)} icon={<CodeOutlined />}>
                            {params.agentTool.language}
                        </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="Version">
                        {params.agentTool.version || 'N/A'}
                    </Descriptions.Item>
                    <Descriptions.Item label="Description" span={2}>
                        {params.agentTool.description}
                    </Descriptions.Item>
                    <Descriptions.Item label="Created">
                        <TimeAgo date={params.agentTool.createdAt * 1000} locale="en-US" />
                    </Descriptions.Item>
                    <Descriptions.Item label="Last Modified">
                        <TimeAgo date={params.agentTool.lastChangeTimestamp * 1000} locale="en-US" />
                    </Descriptions.Item>
                    {params.agentTool.deployedAt && (
                        <Descriptions.Item label="Deployed At" span={2}>
                            <TimeAgo date={params.agentTool.deployedAt * 1000} locale="en-US" />
                        </Descriptions.Item>
                    )}
                </Descriptions>
            </Card>

            {/* Input Parameters */}
            {params.agentTool.inputs && params.agentTool.inputs.length > 0 && (
                <Card title="Input Parameters">
                    <Space direction="vertical" style={{ width: '100%' }}>
                        {params.agentTool.inputs.map((input, index) => (
                            <Card key={input.id} size="small" style={{ backgroundColor: '#fafafa' }}>
                                <Descriptions size="small" column={1}>
                                    <Descriptions.Item label="Name">
                                        <Space>
                                            <Typography.Text code>{input.name}</Typography.Text>
                                            <Tag color={input.required ? 'red' : 'default'}>
                                                {input.required ? 'Required' : 'Optional'}
                                            </Tag>
                                            <Tag>{input.dataType}</Tag>
                                        </Space>
                                    </Descriptions.Item>
                                    {input.defaultValue && (
                                        <Descriptions.Item label="Default Value">
                                            <Typography.Text code>{input.defaultValue}</Typography.Text>
                                        </Descriptions.Item>
                                    )}
                                    {input.description && (
                                        <Descriptions.Item label="Description">
                                            {input.description}
                                        </Descriptions.Item>
                                    )}
                                </Descriptions>
                            </Card>
                        ))}
                    </Space>
                </Card>
            )}

            {/* Code */}
            <Card title="Source Code">
                <div style={{ maxHeight: '500px', overflow: 'auto' }}>
                    <CodeBlock
                        text={params.agentTool.code}
                        language={getLanguageForCodeBlock(params.agentTool.language)}
                        theme={dracula}
                        showLineNumbers
                    />
                </div>
            </Card>
        </Space>
    );
};

export default AgentToolViewDrawer;
