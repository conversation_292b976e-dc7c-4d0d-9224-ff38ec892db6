import { Routes } from "@/constants";
import { useRouter } from "next/navigation";
import useS<PERSON>, { SWRResponse } from "swr";
import type { SWRConfiguration } from "swr";
import useSWRMutation from 'swr/mutation'
import type { SWRMutationConfiguration } from "swr/mutation";
import fileDownload from 'js-file-download'

export namespace Fetcher {

    export enum FetchResultStatus {
        FAILED_TIMEOUT,
        FAILED_CONNECTION,
        FAILED_LOGGEDOUT,
        FAILED_OTHER,
        SUCCESS_2xx,
        SUCCESS_NOT_2xx,
    }

    export enum FetcherErrorType {
        FAILED_CONNECTION,
        FAILED_LOGGEDOUT,
        FAILED_SERVER_ERROR,
        FAILED_OTHER
    }

    interface FetchRequest {
        method: 'GET' | 'POST' | 'DELETE' | 'PUT';
        headers?: object;
        body?: object;
        queryString?: object;
        timeout?: number;
        auth?: boolean;
        autoLogout?: boolean;
        urlPlaceholders?: object;
    }

    export interface FetchResponse<T> {
        reason: FetchResultStatus,
        status: number;
        response?: T;
    }

    export function GetAuthHeader() {
        return {
            'Authorization': 'Bearer ' + window.localStorage.getItem("jwt:value"),
        }
    }

    export async function sfetch<T>(url: string | URL, params: FetchRequest): Promise<FetchResponse<T>> {

        const controller = new AbortController();
        const timeoutId = (params.timeout != null && params.timeout > 0) ? setTimeout(() => controller.abort(), params.timeout) : null;

        if (typeof url === "string") {
            url = new URL(url);
        }

        Object.entries(params.queryString ?? {}).map(([key, value]) => {
            url.searchParams.append(key, value);
        })

        if (params.auth) {
            params.headers = {
                'Authorization': 'Bearer ' + window.localStorage.getItem("jwt:value"),
                ...params.headers,
            }
        }

        return fetch(
            url,
            {
                signal: controller.signal,
                method: params.method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    ...params.headers
                },
                body: (params.body != null) ? JSON.stringify(params.body) : null
            }
        )
            .then(async response => {
                if (timeoutId != null) clearTimeout(timeoutId);
                if (response.status > 299 || response.status < 200) {
                    if (response.status == 401) {
                        if (params.autoLogout == null || params.autoLogout == true) {
                            window.location.href = '/login';
                        }
                        return { status: 401, reason: FetchResultStatus.FAILED_LOGGEDOUT };
                    }
                    return { status: response.status, reason: FetchResultStatus.SUCCESS_NOT_2xx };
                }
                return { status: response.status, reason: FetchResultStatus.SUCCESS_2xx, response: await response.json() };
            }).catch(({ name, message }) => {
                if (name == 'AbortError') {
                    return { status: 0, reason: FetchResultStatus.FAILED_TIMEOUT };
                } else if (name == 'TypeError' && message.includes('NetworkError')) {
                    return { status: 0, reason: FetchResultStatus.FAILED_CONNECTION };
                }
                console.log("Error Name:", name);
                console.log("Error Msg:", message);
                return { status: 0, reason: FetchResultStatus.FAILED_OTHER };
            });
    }


    export function fetcherSWR<T>(url: string | URL, params: FetchRequest, swrOptions: SWRConfiguration = {}): SWRResponse<T, any, any> {

        if (typeof url === "string") {
            url = new URL(fillPlaceholders(url, params.urlPlaceholders));
        } else {
            if (params.urlPlaceholders !== undefined && params.urlPlaceholders != null) {
                console.error("URL has placeholders, but is a URL type, it will fail to place placeholders.")
            }
        }

        Object.entries(params.queryString ?? {}).map(([key, value]) => {
            url.searchParams.append(key, value);
        })

        if (params.auth) {
            params.headers = {
                'Authorization': 'Bearer ' + window.localStorage.getItem("jwt:value"),
                ...params.headers,
            }
        }

        return useSWR<T, any, any>([url.toString()], () => fetch(
            url,
            {
                method: params.method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    ...params.headers
                },
                body: (params.body != null) ? JSON.stringify(params.body) : null
            }

        ).then(r => r.json()),
            swrOptions
        )
    }

    export function getRawFetcher2(url: string | URL, params: FetchRequest, swrOptions: any = {}): Promise<any> {

        if (typeof url === "string") {
            url = new URL(url);
        }

        Object.entries(params.queryString ?? {}).map(([key, value]) => {
            url.searchParams.append(key, value);
        })

        if (params.auth) {
            params.headers = {
                'Authorization': 'Bearer ' + window.localStorage.getItem("jwt:value"),
                ...params.headers,
            }
        }

        return fetch(
            url,
            {
                method: params.method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    ...params.headers
                },
                body: (params.body != null) ? JSON.stringify(params.body) : null
            }
        ).catch((reason: Error) => {
            if (reason.name == 'TypeError' && reason.message.includes('NetworkError')) {
                const error = new FetcherError("It looks like your connection is down.")
                error.status = 0;
                error.info = {};
                error.type = FetcherErrorType.FAILED_CONNECTION;
                throw (error);
            }

            const error = FetcherError.fromError(reason);
            error.type = FetcherErrorType.FAILED_OTHER;
            error.status = 0;
            error.info = {};
            throw (error);

        }).then(r => {
            let error = null;

            switch (r.status) {
                case 401:
                    error = new FetcherError('You are logged out.');
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_LOGGEDOUT;

                    if (params.autoLogout ?? true) {
                        const router = useRouter();
                        //router.push(Routes.LOGIN);
                        console.log("almost redirected you...")
                    }
                    break;
                case 404:
                    error = new FetcherError("We couldn't find the resource you are looking for. Please check if it still exists.");
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_SERVER_ERROR;
                    break;
                case 500:
                    error = new FetcherError('Something went wrong with your request. Please try again.');
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_SERVER_ERROR;
                    break;
            }

            if (error != null) throw (error);

            return r.json()
        })
    }

    function getRawFetcher3(urls: string[], params: FetchRequest): Promise<any> {

        let urlObjs = urls.map((url: string) => new URL(fillPlaceholders(url, params.urlPlaceholders)))

        urlObjs.forEach((url: URL) => {
            Object.entries(params.queryString ?? {}).map(([key, value]) => {
                url.searchParams.append(key, value);
            })
        })

        if (params.auth) {
            params.headers = {
                'Authorization': 'Bearer ' + window.localStorage.getItem("jwt:value"),
                ...params.headers,
            }
        }

        const f = (u: URL) => fetch(
            u,
            {
                method: params.method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    ...params.headers
                },
                body: (params.body != null) ? JSON.stringify(params.body) : null
            }
        ).catch((reason: Error) => {
            if (reason.name == 'TypeError' && reason.message.includes('NetworkError')) {
                const error = new FetcherError("It looks like your connection is down.")
                error.status = 0;
                error.info = {};
                error.type = FetcherErrorType.FAILED_CONNECTION;
                throw (error);
            }

            const error = FetcherError.fromError(reason);
            error.type = FetcherErrorType.FAILED_OTHER;
            error.status = 0;
            error.info = {};
            throw (error);

        }).then(r => {
            let error = null;

            switch (r.status) {
                case 401:
                    error = new FetcherError('You are logged out.');
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_LOGGEDOUT;

                    if (params.autoLogout ?? true) {
                        const router = useRouter();
                        //router.push(Routes.LOGIN);
                        console.log("almost redirected you...")
                    }
                    break;
                case 404:
                    error = new FetcherError("We couldn't find the resource you are looking for. Please check if it still exists.");
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_SERVER_ERROR;
                    break;
                case 500:
                    error = new FetcherError('Something went wrong with your request. Please try again.');
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_SERVER_ERROR;
                    break;
            }

            if (error != null) throw (error);

            return r.json()
        })

        return Promise.all(urls.map(url => f(url)))
    }

    export class FetcherError extends Error {
        status: number = 200;
        info: any = {};
        type: FetcherErrorType = FetcherErrorType.FAILED_OTHER;

        static fromError(error: Error): FetcherError {
            const e = new FetcherError(error.message);
            e.name = error.name;
            e.cause = error.cause;
            e.stack = error.stack;
            return e;
        }
    }

    /*
    export function SWRFetcher<T>(url: string | URL, params: FetchRequest, swrOptions: SWRConfiguration = {}): SWRResponse<T, any, any> {
        return useSWR<T, any, any>([url.toString()], () => getRawFetcher2(url, params, swrOptions), swrOptions);
    }
    */

    function fillPlaceholders(url: string, placeholders?: object) {
        Object.entries(placeholders ?? {}).forEach(([key, value]) => {
            if (value !== undefined && value != null) {
                url = url.replace(`{${key}}`, value);
            }
        })
        return url;
    }



    export function SWRMutation<TRequestObject extends object>(url: string, params: FetchRequest, swrOptions: SWRMutationConfiguration<any, any, [string, object?], { data: TRequestObject; urlParams?: object }, any> = {}) {
        url = fillPlaceholders(url, params.urlPlaceholders);

        const fetcherProxy = (key: any, { arg }: { arg: { data: TRequestObject } }) => {
            params.body = { ...params.body, ...arg.data } as object;
            return getRawFetcher2(new URL(url), params, swrOptions)
        }

        const { data, error, trigger, isMutating } = useSWRMutation([url.toString()], fetcherProxy, swrOptions)

        return { data: data, error: error, trigger: (data: TRequestObject) => trigger({ data: data }), isMutating: isMutating }
    }

    export function SWRFetcherMulti<T>(urls: string[], params: FetchRequest, swrOptions: SWRConfiguration = {}): SWRResponse<T[], any, any> {
        return useSWR([...urls, params], () => getRawFetcher3(urls, params), swrOptions);
    }

    export function SWRFetcher<T>(url: string, params: FetchRequest, swrOptions: SWRConfiguration = {}): SWRResponse<T, any, any> {
        return useSWR([url.toString(), params.urlPlaceholders ?? {}], () => getRawFetcher3([url.toString()], params), swrOptions);
    }

    export function Download(url: string, params: FetchRequest, fileName: string) {
        let urlObj = new URL(fillPlaceholders(url, params.urlPlaceholders))

        Object.entries(params.queryString ?? {}).map(([key, value]) => {
            urlObj.searchParams.append(key, value);
        })

        if (params.auth) {
            params.headers = {
                'Authorization': 'Bearer ' + window.localStorage.getItem("jwt:value"),
                ...params.headers,
            }
        }

        return fetch(urlObj,
            {
                method: params.method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    ...params.headers
                },
                body: (params.body != null) ? JSON.stringify(params.body) : null
            }
        )
            .then(res => res.blob())
            .then(res => fileDownload(res, fileName))

    }
}