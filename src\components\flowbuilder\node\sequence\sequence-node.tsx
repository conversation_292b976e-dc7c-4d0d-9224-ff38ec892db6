'use client';

import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { <PERSON><PERSON>, Card, Flex, Space, Tag } from 'antd';
import React from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { SequenceNodeModelData } from './sequence-node-model';
import { BaseNodeProps } from '../base-node';
import SequenceNodeDescription from './sequence-node-description';

export const SequenceNodeName = "sequence";

function SequenceNode(props: BaseNodeProps<SequenceNodeModelData>) {
    const outputCount = props.data.baseData.nodeData.outputCount || 2;
    
    return (
        <>
            <Card title={<Flex justify='stretch'>
                <div className='reactflow-wrapper'>
                    <Space>
                        {props.data.baseData.name}
                        <Tag color="default" bordered={false}>Sequence</Tag>
                    </Space>
                </div>
                <div>
                    <Button
                        style={{ width: '30px' }} type="default"
                        icon={<SettingOutlined />} size='small'
                        onClick={(event) => {
                            event.stopPropagation();
                            props.data.events?.onOpenDrawer();
                        }}
                    />
                </div>
            </Flex>}>
                <SequenceNodeDescription {...props.data.baseData} />
            </Card>
            <Handle type="target" position={Position.Left} id="input" />
            {Array.from({ length: outputCount }, (_, index) => (
                <Handle 
                    key={`output-${index}`}
                    type="source" 
                    position={Position.Right} 
                    id={`output-${index}`}
                    style={{ top: `${((index + 1) * 100) / (outputCount + 1)}%` }}
                />
            ))}
        </>
    );
}

export default React.memo((props: BaseNodeProps<SequenceNodeModelData>) => SequenceNode(props));
