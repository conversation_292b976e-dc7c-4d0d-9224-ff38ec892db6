'use client'

import { <PERSON>B<PERSON>, <PERSON>baseActions, KnowledgebaseController, KnowledgeBaseStatus } from "@/models/knowledgebase";
import { <PERSON>ton, Col, Divider, Flex, Form, Input, Result, Row, Skeleton, Space, Spin, Steps } from "antd";
import React, { useEffect } from "react";
import {
    LoadingOutlined,
    CheckCircleOutlined,
    CheckCircleFilled
} from '@ant-design/icons';
import SubmitButton from "@/components/submit-button";
import Title from "antd/es/typography/Title";
import { SWRConfiguration } from "swr";
import FetcherErrorDisplay from "@/components/fetcher-error-display";
import DrawerTemplate from "@/templates/drawer-template";


interface KnowledgebaseDeployDrawerParams {
    kbId: string;
}

interface KnowledgebaseDeployerProps {
    kbId: string;
    onLoadingChange: (isLoading: boolean) => void;
    onDataChange: (data: KnowledgeBase) => void;
}

const getStepFromStatus = (status: KnowledgeBaseStatus) => {
    switch (status) {
        case KnowledgeBaseStatus.SEALING:
            return 0;
        case KnowledgeBaseStatus.DEPLOYING:
            return 1;
        case KnowledgeBaseStatus.SYNCRONIZING:
            return 2;
        case KnowledgeBaseStatus.TAGGING:
            return 3;
        default:
            return -1;
    }
}
const steps = [
    {
        title: 'Sealing',
        description: 'Locking your Knowledge Base'
    },
    {
        title: 'Deploying',
        description: 'Starting the deployment'
    },
    {
        title: 'Syncronizing',
        description: 'Delivering Knowledge Base data to vector storage'
    },
    {
        title: 'Tagging',
        description: 'Wrapping up by tagging files and versions'
    }
]


const KnowledgebaseDeployer: React.FC<KnowledgebaseDeployerProps> = (params) => {
    const knowledgebaseController: KnowledgebaseController = new KnowledgebaseController()

    const [getOptions, setGetOptions] = React.useState<SWRConfiguration>({ revalidateIfStale: false, refreshInterval: 0 });
    const { data, error, isLoading, mutate, isValidating } = knowledgebaseController.useGet([params.kbId], getOptions);

    const [form] = Form.useForm();

    const { data: deployData, error: deployError, trigger, isMutating } = knowledgebaseController.useDeploy(data ? data[0].kbId : '')

    React.useEffect(() => {
        if (!data || !deployData) return;
        mutate(async (data) => {
            if (!data || !deployData) return data;
            data[0] = deployData
            setTimeout(() => setGetOptions({ revalidateIfStale: true, refreshInterval: 5000 }), 5000);
        }, { revalidate: false })
    }, [deployData])

    useEffect(() => { 
        if (data) params.onDataChange(data[0]) 
        if (data && getStepFromStatus(data[0].status) != -1){
            setGetOptions({ revalidateIfStale: true, refreshInterval: 5000 })
        }
    }, [data])
    useEffect(() => params.onLoadingChange(isLoading), [isLoading])

    const buildSteps = (step: number) => {
        return steps.map(
            (value, index) => {
                if (index == step) return {
                    title: <Space><LoadingOutlined />{value.title}</Space>,
                    description: value.description
                }
                else if (index < step) return {
                    title: <Space>{value.title}</Space>,
                    description: value.description,
                }
                else return value;
            }
        )
    }

    if (error || deployError) {
        return <FetcherErrorDisplay error={error ?? deployError} type="page" />
    }

    if (data === undefined) {
        console.log(data);
        return <Skeleton />
    }

    if (knowledgebaseController.can(KnowledgebaseActions.DEPLOY, data[0])) {
        return (
            <>
                <Spin spinning={isLoading || isValidating || isMutating}>
                    <Title level={5}>Deploy a new Tag</Title>
                    <Form form={form} layout="vertical" autoComplete="off" onFinish={() => {
                        if (data[0].status === KnowledgeBaseStatus.READY)
                            trigger({ body: { tag: form.getFieldValue('tag') } })
                    }}>
                        <Form.Item name="name" label="Tag" rules={[{ required: true }, { min: 3 }]}>
                            <Input />
                        </Form.Item>
                        <Form.Item>
                            <Space>
                                <SubmitButton form={form}>Deploy</SubmitButton>
                            </Space>
                        </Form.Item>
                    </Form>
                </Spin>
                {(data[0].tag && data[0].tag.length > 0) ? (
                    <>
                        <Divider>Active Tag</Divider>
                        <Result
                            status="success"
                            title={data[0].tag}
                            subTitle={"Your Knowledge Base has the tag " + data[0].tag + " ready for use."}
                        /></>
                ) : (null)}
            </>
        )
    }


    const step = getStepFromStatus(data[0].status)
    if (step == -1) {
        return <Result
            status="error"
            title="You can't deploy in the current state"
            subTitle="Your knowledgebase is in an state that doesn't allow deploying, please wait until it is in READY state."
        />;
    }

    return (
        <>
            <Title level={5}>Deployment in progress</Title>
            <Steps
                direction="vertical"
                progressDot
                current={step}
                items={buildSteps(step)}
            />
        </>
    )



}

const KnowledgebaseDeployDrawer: React.FC<KnowledgebaseDeployDrawerParams> = (params) => {

    const [data, setData] = React.useState<KnowledgeBase>()
    const [loading, setLoading] = React.useState<boolean>(false);


    return <DrawerTemplate
        title={(data) ? 'Deploy ' + data.name : 'Loading...'}
        isLoading={loading}
    >
        <KnowledgebaseDeployer
            kbId={params.kbId}
            onLoadingChange={(isLoading: boolean) => { setLoading(isLoading) }}
            onDataChange={(knowledge: KnowledgeBase) => { setData(knowledge) }}
        />
    </DrawerTemplate>

}

export default KnowledgebaseDeployDrawer;