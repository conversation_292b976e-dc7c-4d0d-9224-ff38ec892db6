'use client'

import { <PERSON><PERSON><PERSON><PERSON>, AgentAliasController } from "@/models/agentalias";
import AgentAliasList from "./list/agentalias-list";
import { <PERSON><PERSON>, Divider, Drawer, Space, Typography } from "antd";
import ConfirmDeleteModal from "@/components/confirm-delete-modal";
import React from "react";
import {
    PlusOutlined,
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import AgentAliasViewDrawer from "./drawers/view-drawer";
import AgentAliasCreateDrawer from "./drawers/create-drawer";

type ApiKeysComponentProps = any;

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions {
    agentAlias?: AgentAlias;
    open: boolean;
}

const ApiKeysComponent: React.FC<ApiKeysComponentProps> = (props: ApiKeysComponentProps) => {
    const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
    const [mutateObject, setMutateObject] = React.useState<AgentAlias[]>([]);
    const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({ open: false });

    const onCloseDrawer = () => {
        setDrawerOptions(emptyDrawer);
    };

    const onCreate = () => {
        setDrawerOptions({ title: "Create Alias", isOpen: true, component: <AgentAliasCreateDrawer isOpen={true} onSuccess={onSuccess} /> })
    }

    const onView = (record: AgentAlias) => {
        setDrawerOptions({ title: "View Alias", isOpen: true, component: <AgentAliasViewDrawer data={record} onSwap={onSwap} onDelete={onDelete} /> })
    }

    const onSuccess = (record: AgentAlias) => {
        //TODO: create item
        setMutateObject([record])
    }

    const onSwap = (record: AgentAlias) => {

    }

    const onDelete = (record: AgentAlias) => {
        setDeleteModalOptions({ open: true, agentAlias: record })
    }

    const onDeleteConfirm = (record: AgentAlias) => {
        setDeleteModalOptions({ open: false })
        setMutateObject([record])
    }

    const onDeleteCancel = (record: AgentAlias) => {
        setDeleteModalOptions({ open: false })
    }

    return (
        <>
            <ConfirmDeleteModal<AgentAlias>
                controller={new AgentAliasController()}
                open={deleteModalOptions.open}
                objectToDelete={deleteModalOptions.agentAlias}
                onDelete={onDeleteConfirm}
                onCancel={onDeleteCancel}
            />
            <Typography.Title level={2}>
                Agent Aliases
            </Typography.Title>
            <Button size='large' type='primary' onClick={() => onCreate()}>
                <PlusOutlined />Create
            </Button>
            <Divider />
            <AgentAliasList
                onClick={(record) => onView(record)}
                onDelete={(record) => onDelete(record)}
                mutateObjects={mutateObject}
                onSwap={(record) => onSwap(record)}
            />
            <Drawer
                title={drawerOptions.title}
                placement="right"
                size="large"
                onClose={onCloseDrawer}
                open={drawerOptions.isOpen}
                extra={
                    <Space>
                        <Button onClick={onCloseDrawer}>Cancel</Button>
                    </Space>
                }
            >
                {drawerOptions.component}
            </Drawer>
        </>
    )
}


export default ApiKeysComponent;