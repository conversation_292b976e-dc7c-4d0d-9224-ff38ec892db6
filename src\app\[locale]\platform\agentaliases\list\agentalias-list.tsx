import { AgentAliasController } from "@/models/agentalias";
import { Agent<PERSON>lias } from "@/models/agentalias";
import { Button, Dropdown, MenuProps, Space, TableColumnsType, Tag } from "antd";
import { useTimeAgo } from "next-timeago";
import React from "react";
import { SwapOutlined, DeleteOutlined, EllipsisOutlined } from "@ant-design/icons";
import { AgentAliasActions } from "@/models/agentalias";
import DataTable from "@/components/data-table";


interface AgentAliasListParams {
    onClick: (record: AgentAlias) => void;
    onSwap: (record: AgentAlias) => void;
    onDelete: (record: AgentAlias) => void;
    mutateObjects?: AgentAlias[];
}



const AgentAliasList: React.FC<AgentAliasListParams> = (params: AgentAliasListParams) => {

    const agentAliasController: AgentAliasController = new AgentAliasController();
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState(false);
    const { TimeAgo } = useTimeAgo();

    const contextMenuItems = (record: AgentAlias): MenuProps['items'] => ([
        {
            label: 'Swap',
            key: 'swap',
            icon: <SwapOutlined />,
            onClick: () => { if (params.onSwap) params.onSwap(record); },
            disabled: !agentAliasController.can(AgentAliasActions.SWAP, record)
        },
        {
            label: 'Delete',
            key: 'delete',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => { if (params.onDelete) params.onDelete(record); },
            disabled: !agentAliasController.can(AgentAliasActions.DELETE, record)
        },
    ]);

    const contextMenuProps = (record: AgentAlias) => ({
        items: contextMenuItems(record)
    });

    const columns: TableColumnsType<AgentAlias> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        { title: 'Agent Binding', render: (value, record) => {
            return <Space>
                <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{record.agentName}</a>
                <Tag color="default">{record.agentTag}</Tag>
            </Space>
        }},
        {
            title: 'Alias', dataIndex: 'alias', render: (value, record) => {
                return <Tag color="#222">{value}</Tag>;
            }
        }, 
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'Last Modified', dataIndex: 'lastModifiedTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<AgentAlias, AgentAliasController>
            controller={new AgentAliasController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: AgentAlias) => false}
            mutateItems={params.mutateObjects}
            rowKey={(record : AgentAlias) => record.alias + "::" + record.agentId}
        />
    );
}

export default AgentAliasList;
