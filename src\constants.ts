interface Route {
    endpoint: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
}

export namespace APIRoutes {


    export const serviceEndpoint: string = "http://localhost:5083";

    export function GetURL(path: string): URL {
        return new URL(GetURLString(path));
    }

    export function GetURLString(path: string): string {
        return APIRoutes.serviceEndpoint + path
    }

    export enum SignInController {
        SIGNIN = "/signin",
        UPDATE = "/signin/update"
    }

    export enum KnowledgeBaseController {
        LIST = "/knowledgebase",
        CREATE = "/knowledgebase",
        UPDATE = "/knowledgebase/{kbId}",
        GET = "/knowledgebase/{kbId}",
        LIST_AGENTS_FOR_KNOWLEDGEBASE = "/knowledgebase/{kbId}/agents",
        DEPLOY = "/knowledgebase/{kbId}/deploy",
        DELETE = "/knowledgebase/{kbId}/"
    }

    export enum AgentController {
        CREATE = "/agent",
        SEARCH = "/agent/search",
        GET = "/agent/{agentId}",
        LIST = "/agent",
        DELETE = "/agent/{agentId}",
        UPDATE = "/agent/{agentId}",
        DEPLOY = "/agent/{agentId}/deploy",
        LIST_KNOWLEDGEBASES_FOR_AGENT = "/agent/{agentId}/knowledgebases",
        LIST_TAGS = "/agent/{agentId}/tags",
        CREATE_ALIAS = "/agent/{agentId}/alias",
        LIST_ALIASES = "/agent/{agentId}/alias"
    }

    export enum KnowledgeBaseFileController {
        LIST_FILES_FOR_KNOWLEDGEBASE = "/kbfiles",
        DOWNLOAD_KBFILE = "/kbfiles/{fileId}/download",
        UPLOAD = "/kbfiles/{kbId}/upload",
        DELETE_MULTI = '/kbfiles'
    }

    export enum AgentKnowledgebaseController{
        ASSIGN = "/agentkb/assign",
        EXISTS = "/agentkb/exists",
        UNASSIGN = "/agentkb/unassign"
    }

    export enum ApiKeyController{
        LIST = "/apikey",
        DELETE = "/apikey/{apiKey}",
        GET = "/apikey/{apiKey}",
        CREATE = "/apikey"
    }

    export enum AgentAliasController{
        LIST = "/agentalias",
        DELETE = "/agentalias/{aliasId}",
        SWAP = "/agentalias/{aliasId}/swap",
        GET = "/agentalias/{aliasId}",
        CREATE = "/agentalias"
    }

    export enum BehaviorTreeController{
        LIST = "/agentbehaviortree",
        DELETE = "/agentbehaviortree/{id}",
        GET = "/agentbehaviortree/{id}",
        CREATE = "/agentbehaviortree",
        EDIT = "/agentbehaviortree/{id}"
    }

    export enum FlowController{
        LIST = "/flow",
        DELETE = "/flow/{id}",
        GET = "/flow/{id}",
        CREATE = "/flow",
        EDIT = "/flow/{id}"
    }

    export enum FlowTagController{
        LIST = "/flowtag",
        LIST_BY_FLOW = "/flowtag/flow/{flowId}",
        DELETE = "/flowtag/{id}",
        GET = "/flowtag/{id}",
        CREATE = "/flowtag",
        EDIT = "/flowtag/{id}",
        SET_AS_LATEST = "/flowtag/{id}/latest"
    }

    export enum FlowAliasController{
        LIST = "/flowalias",
        LIST_BY_FLOW = "/flowalias/flow/{flowId}",
        DELETE = "/flowalias/{id}",
        GET = "/flowalias/{id}",
        CREATE = "/flowalias",
        EDIT = "/flowalias/{id}",
        SWAP = "/flowalias/{id}/swap",
        CHECK_TAG_USAGE = "/flowalias/tag/{tagId}/usage"
    }

    export enum TraceController{
        LIST = "/trace",
        DELETE = "/trace/{id}",
        GET = "/trace/{id}",
        SEARCH = "/trace/search",
        GET_EVENTS = "/trace/{id}/events"
    }

    export enum MCPServerController{
        LIST = "/mcpserver",
        DELETE = "/mcpserver/{id}",
        GET = "/mcpserver/{id}",
        CREATE = "/mcpserver",
        EDIT = "/mcpserver/{id}",
        DEPLOY = "/mcpserver/{id}/deploy",
        STOP = "/mcpserver/{id}/stop",
        RESTART = "/mcpserver/{id}/restart"
    }

    export enum AgentToolController{
        LIST = "/agenttool",
        DELETE = "/agenttool/{id}",
        GET = "/agenttool/{id}",
        CREATE = "/agenttool",
        EDIT = "/agenttool/{id}",
        DEPLOY = "/agenttool/{id}/deploy",
        VALIDATE = "/agenttool/{id}/validate",
        DUPLICATE = "/agenttool/{id}/duplicate"
    }

    export enum ToolGroupController{
        LIST = "/toolgroup",
        LIST_WITH_TOOLS = "/toolgroup/withtools",
        DELETE = "/toolgroup/{id}",
        GET = "/toolgroup/{id}",
        GET_WITH_TOOLS = "/toolgroup/{id}/withtools",
        CREATE = "/toolgroup",
        EDIT = "/toolgroup/{id}",
        DUPLICATE = "/toolgroup/{id}/duplicate"
    }
}

export enum Routes {
    LOGIN = "/login",
    HOME = "/platform/home",
    KNOWLEDGEBASE = "/platform/knowledgebase",
    AGENT = "/platform/agent",
    API_KEYS = "/platform/apikeys",
    AGENT_TOOLS = "/platform/agenttools",
}

