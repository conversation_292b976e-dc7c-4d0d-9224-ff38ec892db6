import DrawerTemplate, { DrawerTemplateErrorEmpty } from "@/templates/drawer-template";
import { Button, Card, Form, Input, Select, Space, Typography, message, Transfer } from "antd";
import React, { useEffect, useState } from "react";
import '@ant-design/v5-patch-for-react-19';
import SubmitButton from "@/components/submit-button";
import { ToolGroup, ToolGroupController, ToolGroupCreateRequest, ToolGroupUpdateRequest } from "@/models/toolgroup";
import { AgentTool, AgentToolController } from "@/models/agenttool";

interface ToolGroupEditDrawerParams {
    isOpen: boolean;
    toolGroup?: ToolGroup;
    mode: 'create' | 'edit';
    onSuccess: (data: ToolGroup) => void;
}

interface TransferItem {
    key: string;
    title: string;
    description: string;
    language: string;
    status: string;
}

const ToolGroupEditDrawer: React.FC<ToolGroupEditDrawerParams> = (params: ToolGroupEditDrawerParams) => {
    const toolGroupController: ToolGroupController = new ToolGroupController();
    const agentToolController: AgentToolController = new AgentToolController();
    
    const [form] = Form.useForm<ToolGroup>();
    const [drawerError, setDrawerError] = React.useState(DrawerTemplateErrorEmpty);
    const { data: createData, trigger: createTrigger, isMutating: isCreating } = toolGroupController.useCreate();
    const { data: updateData, trigger: updateTrigger, isMutating: isUpdating } = toolGroupController.useUpdate();
    const { data: toolsData, isLoading: isLoadingTools } = agentToolController.useList({ count: 1000 });
    
    const [selectedToolIds, setSelectedToolIds] = useState<string[]>([]);
    const [transferData, setTransferData] = useState<TransferItem[]>([]);

    const isMutating = isCreating || isUpdating;

    useEffect(() => {
        const data = createData || updateData;
        if (data === undefined) return;
        setDrawerError(DrawerTemplateErrorEmpty);
        if (params.onSuccess != null) params.onSuccess(data);
    }, [createData, updateData]);

    useEffect(() => {
        if (params.isOpen) {
            form.resetFields();
            setDrawerError(DrawerTemplateErrorEmpty);
            if (params.mode === 'edit' && params.toolGroup) {
                form.setFieldsValue({
                    name: params.toolGroup.name,
                    description: params.toolGroup.description
                });
                setSelectedToolIds(params.toolGroup.toolIds || []);
            } else {
                setSelectedToolIds([]);
            }
        }
    }, [params.isOpen, params.toolGroup, params.mode]);

    useEffect(() => {
        if (toolsData?.entries) {
            const transferItems: TransferItem[] = toolsData.entries.map((tool: AgentTool) => ({
                key: tool.id,
                title: tool.name,
                description: tool.description,
                language: tool.language,
                status: tool.status
            }));
            setTransferData(transferItems);
        }
    }, [toolsData]);

    const onFinish = async (values: any) => {
        try {
            if (params.mode === 'create') {
                const request: ToolGroupCreateRequest = {
                    name: values.name,
                    description: values.description,
                    toolIds: selectedToolIds
                };
                await createTrigger(request);
            } else if (params.toolGroup) {
                const request: ToolGroupUpdateRequest = {
                    id: params.toolGroup.id,
                    name: values.name,
                    description: values.description,
                    toolIds: selectedToolIds
                };
                await updateTrigger(request);
            }
        } catch (error) {
            message.error('Failed to save tool group');
        }
    };

    const handleTransferChange = (targetKeys: string[]) => {
        setSelectedToolIds(targetKeys);
    };

    const renderTransferItem = (item: TransferItem) => {
        return {
            label: (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                    <div>
                        <div style={{ fontWeight: 'bold' }}>{item.title}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>{item.description}</div>
                    </div>
                    <div style={{ display: 'flex', gap: '4px' }}>
                        <span style={{ 
                            fontSize: '10px', 
                            padding: '2px 6px', 
                            borderRadius: '3px', 
                            backgroundColor: '#f0f0f0',
                            color: '#666'
                        }}>
                            {item.language}
                        </span>
                        <span style={{ 
                            fontSize: '10px', 
                            padding: '2px 6px', 
                            borderRadius: '3px', 
                            backgroundColor: item.status === 'READY' ? '#f6ffed' : '#fff2e8',
                            color: item.status === 'READY' ? '#52c41a' : '#fa8c16'
                        }}>
                            {item.status}
                        </span>
                    </div>
                </div>
            ),
            value: item.key
        };
    };

    return (
        <DrawerTemplate
            error={drawerError}
            isLoading={isMutating}
            title={(params.mode === 'create') ? 'Create Tool Group' : 'Edit ' + params.toolGroup?.name}
        >
            <Form form={form} layout='vertical' onFinish={onFinish} scrollToFirstError={{ behavior: 'instant', block: 'end', focus: true }}>
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[
                        { max: 64, message: 'Please input up to 64 characters.' },
                        { min: 3, message: 'Group name must have at least 3 characters.' },
                        { required: true, message: 'Please input a name for the group' }
                    ]}
                >
                    <Input />
                </Form.Item>
                
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[
                        { max: 250, message: 'Please input a description of up to 250 characters' },
                        { required: true, message: 'Please input a description' }
                    ]}
                >
                    <Input.TextArea rows={3} count={{ show: true, max: 250 }} />
                </Form.Item>

                <Form.Item label="Select Tools">
                    <Typography.Text type="secondary" style={{ marginBottom: 16, display: 'block' }}>
                        Choose which tools to include in this group. You can search and filter tools on both sides.
                    </Typography.Text>
                    <Transfer
                        dataSource={transferData}
                        targetKeys={selectedToolIds}
                        onChange={handleTransferChange}
                        render={renderTransferItem}
                        titles={['Available Tools', 'Selected Tools']}
                        showSearch
                        filterOption={(inputValue, item) =>
                            item.title.toLowerCase().includes(inputValue.toLowerCase()) ||
                            item.description.toLowerCase().includes(inputValue.toLowerCase())
                        }
                        listStyle={{
                            width: 300,
                            height: 400,
                        }}
                        operations={['Add to Group', 'Remove from Group']}
                        showSelectAll={false}
                        loading={isLoadingTools}
                    />
                    <Typography.Text type="secondary" style={{ marginTop: 8, display: 'block' }}>
                        Selected {selectedToolIds.length} tool(s)
                    </Typography.Text>
                </Form.Item>

                <Form.Item>
                    <SubmitButton form={form}>
                        {(params.mode === 'create') ? 'Create Group' : 'Save Changes'}
                    </SubmitButton>
                </Form.Item>
            </Form>
        </DrawerTemplate>
    );
};

export default ToolGroupEditDrawer;
