'use client';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { <PERSON><PERSON>, Card, Flex, Space, Tag } from 'antd'; import React from 'react';
import { SettingOutlined } from '@ant-design/icons'; import { TaskNodeModelData } from './task-node-model';
import { BaseNodeProps } from '../base-node'; import TaskNodeDescription from './task-node-description';
export const TaskNodeName = "task";

function TaskNode(props: BaseNodeProps<TaskNodeModelData>) {
    return (<>
        <Card title={<Flex justify='stretch'>
            <div className='reactflow-wrapper'>
                <Space>
                    {props.data.baseData.name}
                    <Tag color="default" bordered={false}>Task</Tag>
                </Space>
            </div>
            <div>
                <Button
                    style={{ width: '30px' }} type="default"
                    icon={<SettingOutlined />} size='small'
                    onClick={(event) => {
                        event.stopPropagation();
                        props.data.events?.onOpenDrawer();
                    }}
                />
            </div>
        </Flex>}>
            <TaskNodeDescription {...props.data.baseData} />
        </Card>
        <Handle type="target" position={Position.Left} id="trigger" />
        <Handle type="source" position={Position.Right} id="next" />
    </>);
}

export default React.memo((props: BaseNodeProps<TaskNodeModelData>) => TaskNode(props));























