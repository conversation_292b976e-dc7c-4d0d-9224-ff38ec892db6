import { Flow, FlowController, FlowCreateRequest, FlowUpdateRequest } from "@/models/flow";
import { Button, Form, Input, Space, message } from "antd";
import React from "react";

interface FlowPutDrawerProps {
    isOpen: boolean;
    onSuccess: (flow: Flow) => void;
    mode: 'create' | 'edit';
    flow?: Flow;
}

const FlowPutDrawer: React.FC<FlowPutDrawerProps> = ({ isOpen, onSuccess, mode, flow }) => {
    const [form] = Form.useForm();
    const flowController = new FlowController();
    const { trigger: createTrigger, isMutating: isCreating } = flowController.useCreate();
    const { trigger: updateTrigger, isMutating: isUpdating } = flowController.useUpdate();

    React.useEffect(() => {
        if (isOpen) {
            if (mode === 'edit' && flow) {
                form.setFieldsValue({
                    name: flow.name,
                    description: flow.description
                });
            } else {
                form.resetFields();
            }
        }
    }, [isOpen, mode, flow, form]);

    const handleSubmit = async (values: any) => {
        try {
            let result;
            if (mode === 'create') {
                const request: FlowCreateRequest = {
                    name: values.name,
                    description: values.description
                };
                result = await createTrigger(request);
            } else if (mode === 'edit' && flow) {
                const request: FlowUpdateRequest = {
                    id: flow.id,
                    name: values.name,
                    description: values.description
                };
                result = await updateTrigger(request);
            }

            if (result) {
                message.success(`Flow ${mode === 'create' ? 'created' : 'updated'} successfully`);
                onSuccess(result);
            }
        } catch (error) {
            message.error(`Failed to ${mode} flow`);
        }
    };

    const isLoading = isCreating || isUpdating;

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            disabled={isLoading}
        >
            <Form.Item
                name="name"
                label="Flow Name"
                rules={[
                    { required: true, message: 'Please enter a flow name' },
                    { min: 2, message: 'Flow name must be at least 2 characters' }
                ]}
            >
                <Input placeholder="Enter flow name" />
            </Form.Item>

            <Form.Item
                name="description"
                label="Description"
                rules={[
                    { required: true, message: 'Please enter a description' }
                ]}
            >
                <Input.TextArea 
                    rows={4} 
                    placeholder="Enter flow description"
                />
            </Form.Item>

            <Form.Item>
                <Space>
                    <Button 
                        type="primary" 
                        htmlType="submit" 
                        loading={isLoading}
                    >
                        {mode === 'create' ? 'Create' : 'Update'} Flow
                    </Button>
                </Space>
            </Form.Item>
        </Form>
    );
};

export default FlowPutDrawer;
