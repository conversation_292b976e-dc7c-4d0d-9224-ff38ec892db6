import { But<PERSON>, Dropdown, MenuProps, Space, TableColumnsType, Tag } from "antd";
import {
    RocketOutlined,
    DeleteOutlined,
    EllipsisOutlined,
    EditOutlined,
    CheckCircleOutlined,
    CopyOutlined,
    CodeOutlined
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";
import DataTable from "@/components/data-table";
import React from "react";
import { AgentTool, AgentToolActions, AgentToolController, AgentToolLanguage, AgentToolStatus } from "@/models/agenttool";

interface AgentToolListParams {
    onClick: (record: AgentTool) => void;
    onDelete: (record: AgentTool) => void;
    onEdit: (record: AgentTool) => void;
    onDeploy: (record: AgentTool) => void;
    onValidate: (record: AgentTool) => void;
    onDuplicate: (record: AgentTool) => void;
    mutateObjects?: AgentTool[];
}

const AgentToolList: React.FC<AgentToolListParams> = (params: AgentToolListParams) => {
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState(false);
    const { TimeAgo } = useTimeAgo();

    const contextMenuProps = (record: AgentTool): MenuProps => {
        const controller = new AgentToolController();
        return {
            items: [
                {
                    key: 'edit',
                    label: 'Edit',
                    icon: <EditOutlined />,
                    disabled: !controller.can(AgentToolActions.EDIT, record),
                    onClick: () => params.onEdit(record)
                },
                {
                    key: 'validate',
                    label: 'Validate',
                    icon: <CheckCircleOutlined />,
                    disabled: !controller.can(AgentToolActions.VALIDATE, record),
                    onClick: () => params.onValidate(record)
                },
                {
                    key: 'deploy',
                    label: 'Deploy',
                    icon: <RocketOutlined />,
                    disabled: !controller.can(AgentToolActions.DEPLOY, record),
                    onClick: () => params.onDeploy(record)
                },
                {
                    key: 'duplicate',
                    label: 'Duplicate',
                    icon: <CopyOutlined />,
                    disabled: !controller.can(AgentToolActions.DUPLICATE, record),
                    onClick: () => params.onDuplicate(record)
                },
                {
                    type: 'divider'
                },
                {
                    key: 'delete',
                    label: 'Delete',
                    icon: <DeleteOutlined />,
                    disabled: !controller.can(AgentToolActions.DELETE, record),
                    onClick: () => params.onDelete(record),
                    danger: true
                }
            ]
        };
    };

    const getLanguageColor = (language: AgentToolLanguage): string => {
        switch (language) {
            case AgentToolLanguage.JAVA:
                return 'orange';
            case AgentToolLanguage.CSHARP:
                return 'purple';
            case AgentToolLanguage.PYTHON:
                return 'green';
            default:
                return 'default';
        }
    };

    const getStatusColor = (status: AgentToolStatus): string => {
        switch (status) {
            case AgentToolStatus.DRAFT:
                return 'default';
            case AgentToolStatus.VALIDATING:
                return 'processing';
            case AgentToolStatus.READY:
                return 'success';
            case AgentToolStatus.FAILED:
                return 'error';
            case AgentToolStatus.DEPLOYING:
                return 'processing';
            case AgentToolStatus.DEPLOYED:
                return 'cyan';
            default:
                return 'default';
        }
    };

    const columns: TableColumnsType<AgentTool> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Name', dataIndex: 'name', render: (value, record) => {
                return <a onClick={(e) => {
                    e.preventDefault();
                    if (params.onClick) {
                        params.onClick(record);
                    }
                }}>{value}</a>;
            }
        },
        { title: 'Description', dataIndex: 'description' },
        {
            title: 'Language', dataIndex: 'language', render: (language: AgentToolLanguage) => {
                return <Tag color={getLanguageColor(language)} icon={<CodeOutlined />}>
                    {language}
                </Tag>;
            }
        },
        {
            title: 'Status', dataIndex: 'status', render: (status: AgentToolStatus) => {
                return <Tag color={getStatusColor(status)}>
                    {status}
                </Tag>;
            }
        },
        {
            title: 'Inputs', dataIndex: 'inputs', render: (inputs: any[]) => {
                return inputs ? inputs.length : 0;
            }
        },
        {
            title: 'Version', dataIndex: 'version', render: (version: number) => {
                return version || 'N/A';
            }
        },
        {
            title: 'Last Modified', dataIndex: 'lastChangeTimestamp', render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<AgentTool, AgentToolController>
            controller={new AgentToolController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: AgentTool) => 
                entry.status === AgentToolStatus.VALIDATING || 
                entry.status === AgentToolStatus.DEPLOYING
            }
            mutateItems={params.mutateObjects}
            rowKey="id"
        />
    )
}

export default AgentToolList;
