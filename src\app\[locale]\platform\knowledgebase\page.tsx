'use client'

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Space, TableColumnsType, Typography } from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import ListKnowledgeBasesComponent from './list'
import { KnowledgeBase, KnowledgebaseController } from '@/models/knowledgebase';
import PutKnowledgebaseForm from './put-form';
import { DrawerFormTemplateProperties } from '@/templates/drawer-template';
import '@ant-design/v5-patch-for-react-19';
import DataTable from '@/components/data-table';
import KnowledgebaseEditDrawer from './drawers/edit-drawer';
import KnowledgebaseList from './lists/knowledgebase-list';
import KnowledgebaseViewDrawer from './drawers/view-drawer';
import KnowledgebaseDeployDrawer from './drawers/deploy-drawer';
import ConfirmDeleteModal from '@/components/confirm-delete-modal';

type KnowledgeComponentProps = any;

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions{
  knowledgebase? : KnowledgeBase;
  open: boolean;
}

const KnowledgeComponent: React.FC<KnowledgeComponentProps> = (props: KnowledgeComponentProps) => {
  const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
  const [mutateObject, setMutateObject] = React.useState<KnowledgeBase[]>([]);
  const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({open: false});

  
  const onCloseDrawer = () => {
    setDrawerOptions(emptyDrawer);
  };

  const onEdit = (record : KnowledgeBase) => {
    setDrawerOptions(
      {
        title: "Edit Knowledge Base",
        isOpen: true,
        component: <KnowledgebaseEditDrawer
        knowledgebase={record}
          isOpen={true}
          onSuccess={(data: KnowledgeBase) => { setDrawerOptions(emptyDrawer); setMutateObject([data]) }}
          mode='edit'
        />
      }
    )
  }

  const onCreate = () => {
    setDrawerOptions(
      {
        title: "Create Knowledge Base",
        isOpen: true,
        component: <KnowledgebaseEditDrawer
          isOpen={true}
          onSuccess={(data: KnowledgeBase) => { setDrawerOptions(emptyDrawer); setMutateObject([data]) }}
          mode='create'
        />
      }
    )
  }

  const onView = (record : KnowledgeBase) => {
    setDrawerOptions(
      {
        title: "View Knowledge Base",
        isOpen: true,
        component: <KnowledgebaseViewDrawer 
          knowledgebase={record}
          onDelete={(record : KnowledgeBase) => onDelete(record)}
          onDeploy={(record : KnowledgeBase) => onDeploy(record)}
          onEdit={(record : KnowledgeBase) => onEdit(record)}
        />
      }
    )
  }

  const onDeploy = (record : KnowledgeBase) => {
    setDrawerOptions(
      {
        title: "Deploy Knowledge Base",
        isOpen: true,
        component: <KnowledgebaseDeployDrawer 
          kbId={record.kbId}
        />
      }
    )
  }

  const onDelete = (record : KnowledgeBase) => {
    setDeleteModalOptions({open: true, knowledgebase: record})
  }

  const onDeleteConfirm = (record : KnowledgeBase) => {
    setDeleteModalOptions({open:false})
    setMutateObject([record])
  }

  const onDeleteCancel = (record : KnowledgeBase) => {
    setDeleteModalOptions({open:false})
  }


  return (
    <>
      <ConfirmDeleteModal<KnowledgeBase>
        controller={new KnowledgebaseController()}
        open={deleteModalOptions.open}
        objectToDelete={deleteModalOptions.knowledgebase}
        onDelete={onDeleteConfirm}
        onCancel={onDeleteCancel}
      />
      <Typography.Title level={2}>
        Knowledge Bases
      </Typography.Title>
      <Button size='large' type='primary' onClick={() => onCreate()}>
        <PlusOutlined />Create
      </Button>
      <Divider />
      <KnowledgebaseList
        onClick={(record : KnowledgeBase) => onView(record)}
        onDelete={(record : KnowledgeBase) => onDelete(record)}
        onDeploy={(record : KnowledgeBase) => onDeploy(record)}
        onEdit={(record : KnowledgeBase) => onEdit(record)}
        onUploadData={(record : KnowledgeBase) => {}}
        mutateObjects={mutateObject}
      />
      <Drawer
        title={drawerOptions.title}
        placement="right"
        size="large"
        onClose={onCloseDrawer}
        open={drawerOptions.isOpen}
        extra={
          <Space>
            <Button onClick={onCloseDrawer}>Cancel</Button>
          </Space>
        }
      >
        {drawerOptions.component}
      </Drawer>
    </>
  );
}

export default KnowledgeComponent;