import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Divider, <PERSON>, Typography } from "antd";
import { ConditionalClause, ConditionalTaskData } from "./conditional-task";
import Condition from "@/components/flowbuilder/conditions/condition";
import { Field, OptionGroup, RuleGroupType } from "react-querybuilder";
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { BaseTaskProps } from "../../flowbuilder/node/task-node/base-task";
import DummyTaskForm from "./dummy-task-form";
import { DummyTaskData } from "./dummy-task";
import TaskTypeSelect, { TaskTypeOption } from "./task-type-select";

const { Panel } = Collapse;

interface ConditionalTaskFormProps {
    value: ConditionalTaskData;
    onChange: (data: ConditionalTaskData) => void;
    fields: OptionGroup<Field>[];
}

// Task type registry for conditional tasks
// This is a subset of the main task types, as we might not want to allow
// all task types within a conditional task
const conditionalTaskTypes: TaskTypeOption[] = [
    { label: 'Dummy Task', value: 'dummy' }
];

// Task form registry
const taskForms: Record<string, React.ComponentType<any>> = {
    'dummy': DummyTaskForm
};

const ConditionalTaskForm: React.FC<ConditionalTaskFormProps> = ({ value, onChange, fields }) => {
    const [selectedTaskType, setSelectedTaskType] = useState<string | null>(null);
    const [activeClauseIndex, setActiveClauseIndex] = useState<number | null>(null);

    // Add a new clause
    const handleAddClause = () => {
        const newClauses = [...value.clauses];
        newClauses.push({
            condition: { combinator: 'and', rules: [] },
            tasks: []
        });
        onChange({ ...value, clauses: newClauses });
        setActiveClauseIndex(newClauses.length - 1);
    };

    // Update a clause's condition
    const handleConditionChange = (index: number, condition: RuleGroupType) => {
        const newClauses = [...value.clauses];
        newClauses[index].condition = condition;
        onChange({ ...value, clauses: newClauses });
    };

    // Delete a clause
    const handleDeleteClause = (index: number) => {
        const newClauses = [...value.clauses];
        newClauses.splice(index, 1);
        onChange({ ...value, clauses: newClauses });
    };

    // Add a task to a clause
    const handleAddTask = (clauseIndex: number | null) => {
        if (!selectedTaskType) return;

        let newValue = { ...value };
        
        if (clauseIndex !== null) {
            // Add to specific clause
            if (newValue.clauses[clauseIndex].tasks.length >= 10) return;
            
            const newTasks = [...newValue.clauses[clauseIndex].tasks];
            if (selectedTaskType === 'dummy') {
                newTasks.push({
                    name: 'Dummy Task',
                    taskData: { message: '' } as DummyTaskData
                });
            }
            
            newValue.clauses[clauseIndex].tasks = newTasks;
        } else {
            // Add to else clause
            if (newValue.elseTasks.length >= 10) return;
            
            const newTasks = [...newValue.elseTasks];
            if (selectedTaskType === 'dummy') {
                newTasks.push({
                    name: 'Dummy Task',
                    taskData: { message: '' } as DummyTaskData
                });
            }
            
            newValue.elseTasks = newTasks;
        }
        
        onChange(newValue);
        setSelectedTaskType(null);
    };

    // Update a task in a clause
    const handleTaskChange = (clauseIndex: number | null, taskIndex: number, taskData: any) => {
        let newValue = { ...value };
        
        if (clauseIndex !== null) {
            // Update in specific clause
            const newTasks = [...newValue.clauses[clauseIndex].tasks];
            newTasks[taskIndex] = { ...newTasks[taskIndex], taskData };
            newValue.clauses[clauseIndex].tasks = newTasks;
        } else {
            // Update in else clause
            const newTasks = [...newValue.elseTasks];
            newTasks[taskIndex] = { ...newTasks[taskIndex], taskData };
            newValue.elseTasks = newTasks;
        }
        
        onChange(newValue);
    };

    // Delete a task from a clause
    const handleDeleteTask = (clauseIndex: number | null, taskIndex: number) => {
        let newValue = { ...value };
        
        if (clauseIndex !== null) {
            // Delete from specific clause
            const newTasks = [...newValue.clauses[clauseIndex].tasks];
            newTasks.splice(taskIndex, 1);
            newValue.clauses[clauseIndex].tasks = newTasks;
        } else {
            // Delete from else clause
            const newTasks = [...newValue.elseTasks];
            newTasks.splice(taskIndex, 1);
            newValue.elseTasks = newTasks;
        }
        
        onChange(newValue);
    };

    // Render task form based on task type
    const renderTaskForm = (clauseIndex: number | null, taskIndex: number, task: BaseTaskProps<any>) => {
        const TaskFormComponent = taskForms[task.name.toLowerCase().replace(' ', '-')] || taskForms['dummy'];
        
        return (
            <Card 
                key={taskIndex} 
                title={`Task ${taskIndex + 1}: ${task.name}`}
                extra={
                    <Button 
                        icon={<DeleteOutlined />} 
                        danger 
                        onClick={() => handleDeleteTask(clauseIndex, taskIndex)}
                    />
                }
                style={{ marginBottom: 10 }}
            >
                <TaskFormComponent 
                    value={task.taskData} 
                    onChange={(data: any) => handleTaskChange(clauseIndex, taskIndex, data)} 
                />
            </Card>
        );
    };

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            <Typography.Title level={5}>Conditional Logic</Typography.Title>
            
            {/* IF/ELSE IF clauses */}
            {value.clauses.map((clause, index) => (
                <Card 
                    key={index} 
                    title={index === 0 ? 'IF' : 'ELSE IF'} 
                    extra={
                        <Button 
                            icon={<DeleteOutlined />} 
                            danger 
                            onClick={() => handleDeleteClause(index)}
                        />
                    }
                >
                    <Collapse 
                        activeKey={activeClauseIndex === index ? ['condition'] : []}
                        onChange={() => setActiveClauseIndex(activeClauseIndex === index ? null : index)}
                    >
                        <Panel header="Condition" key="condition">
                            <Condition 
                                fields={fields} 
                                query={clause.condition} 
                                onChange={(query) => handleConditionChange(index, query)}
                                onCancel={() => setActiveClauseIndex(null)}
                            />
                        </Panel>
                    </Collapse>
                    
                    <Divider>Tasks</Divider>
                    
                    {clause.tasks.map((task, taskIndex) => 
                        renderTaskForm(index, taskIndex, task)
                    )}
                    
                    {clause.tasks.length < 10 && (
                        <TaskTypeSelect
                            value={selectedTaskType}
                            onChange={setSelectedTaskType}
                            onAddTask={() => handleAddTask(index)}
                            options={conditionalTaskTypes}
                        />
                    )}
                </Card>
            ))}
            
            <Button 
                type="dashed" 
                icon={<PlusOutlined />} 
                onClick={handleAddClause}
                style={{ marginBottom: 16 }}
            >
                Add {value.clauses.length === 0 ? 'IF' : 'ELSE IF'} Clause
            </Button>
            
            {/* ELSE clause */}
            <Card title="ELSE">
                {value.elseTasks.map((task, taskIndex) => 
                    renderTaskForm(null, taskIndex, task)
                )}
                
                {value.elseTasks.length < 10 && (
                    <TaskTypeSelect
                        value={selectedTaskType}
                        onChange={setSelectedTaskType}
                        onAddTask={() => handleAddTask(null)}
                        options={conditionalTaskTypes}
                    />
                )}
            </Card>
        </Space>
    );
};

export default ConditionalTaskForm;
