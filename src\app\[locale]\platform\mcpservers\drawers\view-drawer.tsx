import { MCPServer, MCPServerStatus } from "@/models/mcpserver";
import { Button, Descriptions, Space, Tag, Typography, Alert } from "antd";
import React from "react";
import {
    PlayCircleOutlined,
    StopOutlined,
    ReloadOutlined,
    LinkOutlined,
} from '@ant-design/icons';
import { useTimeAgo } from "next-timeago";

interface MCPServerViewDrawerProps {
    mcpServer: MCPServer;
    isOpen: boolean;
    onClose: () => void;
}

const MCPServerViewDrawer: React.FC<MCPServerViewDrawerProps> = ({ mcpServer, isOpen, onClose }) => {
    const getStatusColor = (status: MCPServerStatus) => {
        switch (status) {
            case MCPServerStatus.CREATED:
                return 'default';
            case MCPServerStatus.DEPLOYING:
                return 'processing';
            case MCPServerStatus.RUNNING:
                return 'success';
            case MCPServerStatus.STOPPED:
                return 'default';
            case MCPServerStatus.FAILED:
                return 'error';
            case MCPServerStatus.UPDATING:
                return 'processing';
            default:
                return 'default';
        }
    };

    const getStatusDescription = (status: MCPServerStatus) => {
        switch (status) {
            case MCPServerStatus.CREATED:
                return 'Server has been created but not yet deployed';
            case MCPServerStatus.DEPLOYING:
                return 'Server is currently being deployed';
            case MCPServerStatus.RUNNING:
                return 'Server is running and available';
            case MCPServerStatus.STOPPED:
                return 'Server has been stopped';
            case MCPServerStatus.FAILED:
                return 'Server deployment or operation failed';
            case MCPServerStatus.UPDATING:
                return 'Server is being updated';
            default:
                return 'Unknown status';
        }
    };

    return (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Typography.Title level={4}>MCP Server Information</Typography.Title>
            
            <Descriptions bordered column={1}>
                <Descriptions.Item label="Name">
                    {mcpServer.name}
                </Descriptions.Item>
                <Descriptions.Item label="Description">
                    {mcpServer.description}
                </Descriptions.Item>
                <Descriptions.Item label="Flow">
                    {mcpServer.flowName || 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="Flow Alias">
                    {mcpServer.flowAlias}
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                    <Space direction="vertical" size="small">
                        <Tag color={getStatusColor(mcpServer.status)}>
                            {mcpServer.status}
                        </Tag>
                        <span style={{ color: '#666', fontSize: '12px' }}>
                            {getStatusDescription(mcpServer.status)}
                        </span>
                    </Space>
                </Descriptions.Item>
                {mcpServer.endpoint && (
                    <Descriptions.Item label="Endpoint">
                        <Space>
                            <LinkOutlined />
                            <a href={mcpServer.endpoint} target="_blank" rel="noopener noreferrer">
                                {mcpServer.endpoint}
                            </a>
                        </Space>
                    </Descriptions.Item>
                )}
                {mcpServer.deployedAt && (
                    <Descriptions.Item label="Deployed At">
                        <TimeAgo date={mcpServer.deployedAt * 1000} locale="en-US" live={true} />
                    </Descriptions.Item>
                )}
                <Descriptions.Item label="Created At">
                    <TimeAgo date={mcpServer.createdAt * 1000} locale="en-US" live={true} />
                </Descriptions.Item>
                <Descriptions.Item label="Last Modified">
                    <TimeAgo date={mcpServer.lastChangeTimestamp * 1000} locale="en-US" live={true} />
                </Descriptions.Item>
            </Descriptions>

            {mcpServer.status === MCPServerStatus.FAILED && (
                <Alert
                    message="Deployment Failed"
                    description="The MCP server deployment failed. Please check the logs or try deploying again."
                    type="error"
                    showIcon
                />
            )}

            {mcpServer.status === MCPServerStatus.RUNNING && mcpServer.endpoint && (
                <Alert
                    message="Server Running"
                    description={`Your MCP server is running and accessible at ${mcpServer.endpoint}`}
                    type="success"
                    showIcon
                />
            )}

            <Space>
                {mcpServer.status === MCPServerStatus.CREATED && (
                    <Button 
                        type="primary" 
                        icon={<PlayCircleOutlined />}
                    >
                        Deploy Server
                    </Button>
                )}
                
                {mcpServer.status === MCPServerStatus.RUNNING && (
                    <>
                        <Button 
                            icon={<StopOutlined />}
                        >
                            Stop Server
                        </Button>
                        <Button 
                            icon={<ReloadOutlined />}
                        >
                            Restart Server
                        </Button>
                    </>
                )}

                <Button onClick={onClose}>
                    Close
                </Button>
            </Space>
        </Space>
    );
};

export default MCPServerViewDrawer;
