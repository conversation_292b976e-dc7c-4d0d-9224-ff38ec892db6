import DrawerTemplate, { DrawerFormTemplateProperties, DrawerTemplateErrorEmpty } from "@/templates/drawer-template";
import { Button, Checkbox, DatePicker, Form, Input, Typography } from "antd";
import React, { useEffect } from "react";
import '@ant-design/v5-patch-for-react-19';
import { Fetcher } from "@/functions/fetcher";
import SubmitButton from "@/components/submit-button";
import { Api<PERSON>ey, ApiKeyController, ApiKeyCreateRequest } from "@/models/apikey";

interface ApiKeyCreateDrawerParams {
    isOpen: boolean;
    onSuccess: (data: ApiKey) => void;
}

const ApiKeyCreateDrawer: React.FC<ApiKeyCreateDrawerParams> = (params: ApiKeyCreateDrawerParams) => {
    const apiKeyController: ApiKeyController = new ApiKeyController();

    const [form] = Form.useForm<ApiKeyCreateRequest>();
    const [drawerError, setDrawerError] = React.useState(DrawerTemplateErrorEmpty)
    const { data, trigger, isMutating } = apiKeyController.useCreate();

    useEffect(() => {
        if (data === undefined) return;
        setDrawerError(DrawerTemplateErrorEmpty);
        if (params.onSuccess != null) params.onSuccess(data);
    }, [data])

    const onFinish = (values: ApiKeyCreateRequest) => {
        let request : ApiKeyCreateRequest =  { name: values.name, expireAt: Math.floor(values.expireAt / 1000), personalKey: values.personalKey, claims:[] }
        trigger({ body: request }).catch((reason: Fetcher.FetcherError) => {
            setDrawerError({ message: reason.message, title: 'Something went wrong with your request.', show: true });
        });
    };

    return (
        <DrawerTemplate
            error={drawerError}
            isLoading={isMutating}
            title='Create an API Key'
        >
            <Form form={form} layout='vertical' onFinish={onFinish} scrollToFirstError={{ behavior: 'instant', block: 'end', focus: true }}>
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[
                        { max: 64, message: 'Please input up to 64 characters.' },
                        { min: 3, message: 'Your agent name must have at least 3 characters.' }, 
                        { required: true, message: 'Please input a name for the agent' }
                    ]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="expireAt"
                    label="Expiration"
                    required
                    rules={[{ type: 'object' as const, required: true, message: 'Please select time!' }]}
                >
                    <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
                </Form.Item>
                <Form.Item<ApiKeyCreateRequest> name="personalKey" valuePropName="checked" label={null}>
                    <Checkbox>
                        Personal Key
                    </Checkbox>
                </Form.Item>
                <Form.Item>
                    <SubmitButton form={form}>Create</SubmitButton>
                </Form.Item>
            </Form>
        </DrawerTemplate>
    )
}

export default ApiKeyCreateDrawer;