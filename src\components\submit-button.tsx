import { Button, Form, FormInstance } from "antd";
import React from "react";


interface SubmitButtonProps {
    form: FormInstance;
    enabled?: boolean;
}

const SubmitButton: React.FC<React.PropsWithChildren<SubmitButtonProps>> = ({ form, enabled, children }) => {
    const [submittable, setSubmittable] = React.useState<boolean>(false);
    const values = Form.useWatch([], form);

    React.useEffect(() => {
        form
            .validateFields({ validateOnly: true })
            .then(() => setSubmittable(enabled ?? true))
            .catch(() => setSubmittable(false));
    }, [form, values]);

    return (
        <Button type="primary" htmlType="submit" disabled={!submittable}>
            {children}
        </Button>
    );
};

export default SubmitButton;