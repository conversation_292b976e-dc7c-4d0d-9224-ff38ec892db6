'use client';

import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { <PERSON><PERSON>, Card, Flex, Space, Tag } from 'antd';
import React from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { SwitchNodeModelData } from './switch-node-model';
import { BaseNodeProps } from '../base-node';
import SwitchNodeDescription from './switch-node-description';

export const SwitchNodeName = "switch";

function SwitchNode(props: BaseNodeProps<SwitchNodeModelData>) {
    const conditionCount = props.data.baseData.nodeData.conditions.length;
    const totalOutputs = conditionCount + 1; // N conditions + 1 default
    
    return (
        <>
            <Card title={<Flex justify='stretch'>
                <div className='reactflow-wrapper'>
                    <Space>
                        {props.data.baseData.name}
                        <Tag color="default" bordered={false}>Switch</Tag>
                    </Space>
                </div>
                <div>
                    <Button
                        style={{ width: '30px' }} type="default"
                        icon={<SettingOutlined />} size='small'
                        onClick={(event) => {
                            event.stopPropagation();
                            props.data.events?.onOpenDrawer();
                        }}
                    />
                </div>
            </Flex>}>
                <SwitchNodeDescription {...props.data.baseData} />
            </Card>
            <Handle type="target" position={Position.Left} id="input" />
            
            {/* Condition outputs */}
            {props.data.baseData.nodeData.conditions.map((condition, index) => (
                <Handle 
                    key={`condition-${condition.id}`}
                    type="source" 
                    position={Position.Right} 
                    id={`condition-${index}`}
                    style={{ top: `${((index + 1) * 100) / (totalOutputs + 1)}%` }}
                />
            ))}
            
            {/* Default output */}
            <Handle 
                type="source" 
                position={Position.Right} 
                id="default"
                style={{ top: `${(totalOutputs * 100) / (totalOutputs + 1)}%` }}
            />
        </>
    );
}

export default React.memo((props: BaseNodeProps<SwitchNodeModelData>) => SwitchNode(props));
