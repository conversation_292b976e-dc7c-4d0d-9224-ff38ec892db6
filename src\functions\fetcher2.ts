import useSWR, { SWRResponse } from "swr";
import type { SWRConfiguration } from "swr";
import useSWRMutation from 'swr/mutation'
import type { SWRMutationConfiguration } from "swr/mutation";
import fileDownload from 'js-file-download'
import { APIRoutes } from "@/constants";


export namespace Fetcher2 {

    export enum FetchResultStatus {
        FAILED_TIMEOUT,
        FAILED_CONNECTION,
        FAILED_LOGGEDOUT,
        FAILED_OTHER,
        SUCCESS_2xx,
        SUCCESS_NOT_2xx,
    }

    export enum FetcherErrorType {
        FAILED_CONNECTION,
        FAILED_LOGGEDOUT,
        FAILED_SERVER_ERROR,
        FAILED_OTHER,
        FAILED_404
    }

    interface FetchRequest {
        method: 'GET' | 'POST' | 'DELETE' | 'PUT';
        headers?: object;
        body?: object;
        queryString?: object;
        timeout?: number;
        auth?: boolean;
        urlPlaceholders?: object;
    }

    export interface FetchResponse<T> {
        reason: FetchResultStatus,
        status: number;
        response?: T;
    }

    export interface SWRMutationParams {
        urlPlaceholders?: object;
        queryStrings?: object;
        body?: object;
        headers?: object;
    }

    export class FetcherError extends Error {
        status: number = 200;
        info: any = {};
        type: FetcherErrorType = FetcherErrorType.FAILED_OTHER;

        static fromError(error: Error): FetcherError {
            const e = new FetcherError(error.message);
            e.name = error.name;
            e.cause = error.cause;
            e.stack = error.stack;
            return e;
        }
    }

    export function GetAuthHeader() {
        return {
            'Authorization': 'Bearer ' + window.localStorage.getItem("jwt:value"),
        }
    }

    function fillPlaceholders(url: string, placeholders?: object) {
        Object.entries(placeholders ?? {}).forEach(([key, value]) => {
            if (value !== undefined && value != null) {
                url = url.replace("{" + key + "}", value);
            }
        })
        return url;
    }

    function getRawFetcherMulti(urls: string[], params: FetchRequest) {
        let urlObjs = urls.map((url: string) => new URL(fillPlaceholders(url, params.urlPlaceholders)))

        urlObjs.forEach((url: URL) => {
            Object.entries(params.queryString ?? {}).map(([key, value]) => {
                url.searchParams.set(key, value);
            })
        })

        if (params.auth) {
            params.headers = {
                ...GetAuthHeader(),
                ...params.headers,
            }
        }

        const f = (u: URL) => fetch(
            u,
            {
                method: params.method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    ...params.headers
                },
                body: (params.body != null) ? JSON.stringify(params.body) : null
            }
        ).catch((reason: Error) => {
            if (reason.name == 'TypeError' && reason.message.includes('NetworkError')) {
                const error = new FetcherError("It looks like your connection is down.")
                error.status = 0;
                error.info = {};
                error.type = FetcherErrorType.FAILED_CONNECTION;
                throw (error);
            }

            const error = FetcherError.fromError(reason);
            error.type = FetcherErrorType.FAILED_OTHER;
            error.status = 0;
            error.info = {};
            throw (error);
        }).then(r => {
            let error = null;

            switch (r.status) {
                case 401:
                    error = new FetcherError('You are logged out.');
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_LOGGEDOUT;
                    break;
                case 404:
                    error = new FetcherError("We couldn't find the resource you are looking for. Please check if it still exists.");
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_404;
                    break;
                case 500:
                    error = new FetcherError('Something went wrong with your request. Please try again.');
                    error.status = r.status;
                    error.info = {};
                    error.type = FetcherErrorType.FAILED_SERVER_ERROR;
                    break;
            }

            if (error != null) throw (error);

            return r
        });

        return Promise.all(urlObjs.map(url => f(url)))
    }

    function objectToArray(type : string, o? : object){
        o = o ?? {}
        return Object.entries(o).map(([key, value]) => `${type}:${key}=${value}`)
    }

    function getJsonFetcherMulti(urls: string[], params: FetchRequest): Promise<any> {
        return getRawFetcherMulti(urls, params).then(r => Promise.all(r.map(r => r.json())))
    }

    function getJsonFetcher(urls: string[], params: FetchRequest): Promise<any> {
        return getRawFetcherMulti(urls, params).then(r => r[0].json())
    }

    function SWRFetcherMulti<T>(urls: string[], params: FetchRequest, swrOptions: SWRConfiguration = {}): SWRResponse<T[], any, any> {
        return useSWR([...urls, params.method, ...objectToArray("placeholder", params.urlPlaceholders), ...objectToArray("querystring", params.queryString)], () => getJsonFetcherMulti(urls, params), swrOptions);
    }

    function SWRFetcher<T>(url: string, params: FetchRequest, swrOptions: SWRConfiguration = {}): SWRResponse<T, any, any> {
        return useSWR([url.toString(), params.method, ...objectToArray("placeholder", params.urlPlaceholders), ...objectToArray("querystring", params.queryString)], () => getJsonFetcher([url.toString()], params), swrOptions);
    }

    function Download(url: string, params: FetchRequest, fileName: string) {
        return getRawFetcherMulti([url], params).then(r => r[0].blob()).then(res => fileDownload(res, fileName))
    }

    function SWRMutation<TResponse extends object>(url: string, params: FetchRequest, swrMutationOptions: SWRMutationConfiguration<TResponse, any, [string, object?], { data?: SWRMutationParams; urlParams?: object }, any> = {}) {
        const fetcherProxy = (key: any, { arg }: { arg: { data?: SWRMutationParams } }) => {
            params.body = { ...params.body, ...arg.data?.body }
            params.headers = { ...params.headers, ...arg.data?.headers }
            params.queryString = { ...params.queryString, ...arg.data?.queryStrings }
            params.urlPlaceholders = { ...params.urlPlaceholders, ...arg.data?.urlPlaceholders }
            return getJsonFetcher([url], params)
        }

        const { data: data, error, trigger, isMutating } = useSWRMutation([url.toString()], fetcherProxy, swrMutationOptions)

        return { data: data, error: error, trigger: (params?: SWRMutationParams) => trigger({ data: params }), isMutating: isMutating }
    }

    export function SWRMultiTemplate<TResponse extends object>(endpoints: string[], params: FetchRequest, swrOptions: SWRConfiguration = {}) {

        params.auth = true;

        const { data, error, isLoading, isValidating, mutate } = SWRFetcherMulti<TResponse>(
            endpoints.map((endpoint) => APIRoutes.GetURLString(endpoint)),
            params,
            {
                errorRetryCount: 0,
                revalidateOnFocus: false,
                revalidateOnReconnect: false,
                revalidateIfStale: false,
                ...swrOptions
            })

        return {
            data: data as TResponse[],
            error: error,
            isValidating: isValidating,
            isLoading: (!data && !error) || isLoading,
            mutate
        }
    }

    export function SWRTemplate<TResponse extends object>(endpoint: string, params: FetchRequest, swrOptions: SWRConfiguration = {}) {
        params.auth = true;

        const { data, error, isLoading, isValidating, mutate } = SWRFetcher<TResponse>(
            APIRoutes.GetURLString(endpoint),
            params,
            {
                errorRetryCount: 0,
                revalidateOnFocus: false,
                revalidateOnReconnect: false,
                revalidateIfStale: false,
                ...swrOptions
            })
        return {
            data: data as TResponse,
            error: !!error,
            isValidating: isValidating,
            isLoading: (!data && !error) || isLoading,
            mutate
        }
    }

    export function SWRTemplateRaw<TResponse extends object>(endpoint: string, params: FetchRequest, swrOptions: SWRConfiguration = {}) {
        params.auth = true;

        const { data, error, isLoading, isValidating, mutate } = SWRFetcher<TResponse>(
            APIRoutes.GetURLString(endpoint),
            params,
            {
                errorRetryCount: 0,
                revalidateOnFocus: false,
                revalidateOnReconnect: false,
                revalidateIfStale: false,
                ...swrOptions
            })
        return {
            data: data as TResponse,
            error: error,
            isValidating: isValidating,
            isLoading: isLoading,
            mutate
        }
    }

    export function SWRMutationTemplate<TResponse extends object>(endpoint: string, params: FetchRequest, swrOptions: SWRMutationConfiguration<TResponse, any, any, any, any> = {}) {
        params.auth = true;

        const { data, error, trigger, isMutating } = SWRMutation<TResponse>(
            APIRoutes.GetURLString(endpoint),
            params,
            swrOptions)

        return {
            data: data as TResponse,
            error: !!error,
            trigger,
            isMutating: isMutating
        }
    }

    export function DownloadTemplate(endpoint: string, params: FetchRequest, fileName : string) {
        params.auth = true;

        return Download(
            APIRoutes.GetURLString(endpoint),
            params,
            fileName)
    }


    /*export const SWRMultiTemplate = <TResponse extends object>() => {
        return (endpoints: string[], params: FetchRequest, swrOptions: SWRConfiguration = {}) => {
            params.auth = true;
            params.method = 'GET';
 
            const { data, error, isLoading, isValidating, mutate } = SWRFetcherMulti<TResponse>(
                endpoints,
                params,
                {
                    errorRetryCount: 0,
                    revalidateOnFocus: false,
                    revalidateOnReconnect: false,
                    revalidateIfStale: false,
                    ...swrOptions
                })
            return {
                data: data as TResponse[],
                error: !!error,
                isValidating: isValidating,
                isLoading: (!data && !error) || isLoading,
                mutate
            }
        }
    }
 
    export const SWRTemplate = <TResponse extends object>() => {
        return (endpoint: string, params: FetchRequest, swrOptions: SWRConfiguration = {}) => {
            params.auth = true;
            params.method = 'GET';
 
            const { data, error, isLoading, isValidating, mutate } = SWRFetcher<TResponse>(
                endpoint,
                params,
                {
                    errorRetryCount: 0,
                    revalidateOnFocus: false,
                    revalidateOnReconnect: false,
                    revalidateIfStale: false,
                    ...swrOptions
                })
            return {
                data: data as TResponse,
                error: !!error,
                isValidating: isValidating,
                isLoading: (!data && !error) || isLoading,
                mutate
            }
        }
    }

export const SWRMutationTemplate = <TResponse extends object>() => {
    return (endpoint: string, params: FetchRequest, swrOptions: SWRMutationConfiguration<TResponse, any, any, any, any> = {}) => {
        params.auth = true;
        params.method = 'POST';

        const { data, error, trigger, isMutating } = SWRMutation<TResponse>(
            APIRoutes.GetURLString(endpoint),
            params,
            swrOptions)

        return {
            data: data as TResponse,
            error: !!error,
            trigger,
            isMutating: isMutating
        }
    }
};*/

}