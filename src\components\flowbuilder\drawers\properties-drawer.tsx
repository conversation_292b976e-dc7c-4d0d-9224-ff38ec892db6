import { BaseNodeData, BaseNodeFormElement } from "@/components/flowbuilder/node/base-node";
import { Button, Space } from "antd";
import React from "react";

interface EditNodePropertiesDrawerProps<T> {
    nodeData : BaseNodeData<T>;
    nodeForm : React.FC<BaseNodeFormElement<T>>;
    onChangeProperties : (data : BaseNodeData<T>) => void;
}

function EditNodePropertiesDrawer<T>(props: EditNodePropertiesDrawerProps<T>) {
    const [nodeData, setNodeData] = React.useState<BaseNodeData<T>>(structuredClone(props.nodeData));

    return (
        <Space>
            <props.nodeForm onChange={setNodeData} />
            <Button onClick={() => props.onChangeProperties(nodeData)}>Apply</Button>
        </Space>
    )
}

export default EditNodePropertiesDrawer;