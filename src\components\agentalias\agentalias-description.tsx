import { <PERSON><PERSON><PERSON><PERSON> } from "@/models/agentalias"
import { Descriptions, DescriptionsProps, Skeleton, Tag } from "antd";
import { useTimeAgo } from "next-timeago";

export interface AgentAliasDescriptionParams {
    children?: React.JSX.Element;
    locale?: string,
    agentAlias?: AgentAlias,
    layout?: 'vertical' | 'horizontal'
    
}

const AgentAliasDescription: React.FC<AgentAliasDescriptionParams> = (params) => {
    const { TimeAgo } = useTimeAgo();

    const AgentAliasDescriptionFields: DescriptionsProps['items'] = (params.agentAlias) ? [
        {
            key: 'Alias',
            label: 'Alias',
            children: <Tag color="#222">{params.agentAlias.alias}</Tag>,
        },
        {
            key: 'Description',
            label: 'Description',
            children: params.agentAlias.description,
            span: 2,
        },
        {
            key: 'Agent',
            label: 'Agent',
            children: params.agentAlias.agentName,
        },
        {
            key: 'AgentTag',
            label: 'Agent Tag',
            children: (<Tag color="default">{params.agentAlias.agentTag}</Tag>),
        },
        {
            key: 'Last Modified',
            label: 'Last Modified',
            children: (<TimeAgo date={params.agentAlias.lastChangeTimestamp * 1000} locale="en-US" live={true} />),
        },
        {
            key: 'Created At',
            label: 'Created At',
            children: (new Date(params.agentAlias.createdAt * 1000)).toLocaleDateString(params.locale),
        },
    ].filter(item => item.children !== undefined) : [];



    return ((params.agentAlias) ? <Descriptions items={AgentAliasDescriptionFields} layout={params.layout ?? 'horizontal'}></Descriptions> : <Skeleton />)
}

export default AgentAliasDescription;