import { Alert, Spin, Typography } from "antd";


export function makeDrawerFormFields<T>(data: T, excludeColumns : string[] = []): any[] {
    if (data === undefined || data == null) return [];
    let result: any[] = [];
    Object.entries(data).forEach(([key, value]) => {
        if(!excludeColumns.includes(key))
        result.push({ "name": [key], "value": value })
    })
    return result;
}

export interface DrawerFormTemplateProperties<T extends object>{
    isOpen : boolean;
    title? : string;
    onSuccess? : (data : T) => void;
    component : React.JSX.Element;
}

export interface DrawerTemplateError{
    show : boolean;
    title?: string;
    message?: string;
}

interface DrawerTemplatePropertiesWithChildren{
    children?: React.ReactNode;
    title: string;
    error?: DrawerTemplateError,
    isLoading?: boolean;
}

export const DrawerTemplateErrorEmpty : DrawerTemplateError = {show:false, title:'', message:''}

const DrawerTemplate: React.FC<DrawerTemplatePropertiesWithChildren> = ({children, title, error, isLoading}) => {
    return (
        <>
            <Typography.Title level={2}>
                {title}
            </Typography.Title>
            <Spin spinning={isLoading ?? false} delay={0}>
                {children}
            </Spin>
            {(error && error.show) ? <Alert
                    message={error.title}
                    description={error.message}
                    type="error"
                    banner={true}
                    showIcon
                    closable
                /> : null}
        </>
    );
}

export default DrawerTemplate;