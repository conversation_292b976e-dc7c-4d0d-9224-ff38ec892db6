import React from "react";
import { EditOutlined } from '@ant-design/icons';
import { Button, Typography } from "antd";

const { Title } = Typography;
interface EditableTitleProps {
    title: string;
    level: 2 | 1 | 5 | 3 | 4 | undefined;
    onTitleChange: (title: string) => void;
    className?: string;
    editingClassName?: string;
}

export default function EditableTitle(props: EditableTitleProps) {

    const [isEditing, setIsEditing] = React.useState(false);
    const divRef = React.useRef<HTMLDivElement>(null);

    const startEditing = (focus: boolean = true) => {
        setIsEditing(true);
        if (focus) {
            setTimeout(() => {
                divRef.current?.focus(); 
            }, 10); 
        }
    }

    const stopEditing = () => {
        setIsEditing(false);
        props.onTitleChange(divRef.current?.innerText.replace("\n", "") || '');
    }

    return (
        <Title level={props.level}>
        <div 
            className={isEditing ? props.editingClassName : props.className} 
            >
            <div 
                onDragStart={(e) => { divRef.current?.style.setProperty('cursor', 'none'); }}
                onDragEnd={(e) => { divRef.current?.style.setProperty('cursor', 'text'); }}
                contentEditable={isEditing} 
                onClick={(e) => { startEditing(); e.stopPropagation(); }}
                onBlur={() => { stopEditing(); }} 
                style={{ minWidth: '10px', display: 'inline-block', cursor: 'text' }} 
                ref={divRef}
                onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                        stopEditing();
                        e.stopPropagation();
                    }
                }}
                suppressContentEditableWarning={true}
                >
                {props.title}
            </div>
            <Button size="small" type="text" icon={<EditOutlined />} onClick={(e) => { startEditing(); e.stopPropagation(); }} />
        </div>
        </Title>
    )
}