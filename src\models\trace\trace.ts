import { Fetcher2 } from "@/functions/fetcher2";
import { IB<PERSON>model, IBasemodelController, IBaseModelSearchController, ListRequest, ListResponse } from "../base-model";
import { APIRoutes } from "@/constants";
import { SWRConfiguration, KeyedMutator } from "swr";

export interface Trace extends IBasemodel {
    id: string;
    accountId: string;
    traceTime: number;
    sessionId: string;
    flowId?: string;
    flowName?: string;
    status: TraceStatus;
}

export enum TraceStatus {
    RUNNING = 'RUNNING',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED',
    CANCELLED = 'CANCELLED'
}

export enum TraceActions {
    VIEW,
    DELETE
}

export interface TraceSearchRequest {
    query: string;
    limit?: number;
}

export class TraceController implements IBasemodelController<Trace, TraceActions>, IBaseModelSearchController<Trace> {
    getId: (item: Trace) => string = (item) => item.id;
    
    useGet = (ids: string[], options?: SWRConfiguration) => 
        Fetcher2.SWRMultiTemplate<Trace>(
            ids.map((id) => APIRoutes.TraceController.GET.replace("{id}", id)), 
            { method: 'GET' }, 
            options
        )
    
    can = (action: TraceActions, onItem: Trace) => {
        return true;
    }
    
    useList = (request: ListRequest) => 
        Fetcher2.SWRTemplate<ListResponse<Trace>>(
            APIRoutes.TraceController.LIST, 
            {method: 'GET', queryString: request}
        )
    
    useDelete = (id: string) => 
        Fetcher2.SWRMutationTemplate<Trace>(
            APIRoutes.TraceController.DELETE, 
            {method: 'DELETE', urlPlaceholders: {id: id}}
        )

    useSearch = (query?: string, limit: number = 100) => {
        return Fetcher2.SWRTemplateRaw<ListResponse<Trace>>(
            APIRoutes.TraceController.SEARCH,
            {
                method: 'GET', 
                queryString: { query: query, limit: limit }
            },
            {
                revalidateOnMount: false,
                keepPreviousData: true,
                isPaused: () => !query || query.length === 0
            }
        )
    }
}
