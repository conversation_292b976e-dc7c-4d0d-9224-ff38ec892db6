import AgentAliasDescription from "@/components/agentalias/agentalias-description";
import { <PERSON><PERSON><PERSON>s, AgentAliasA<PERSON>, AgentAliasController } from "@/models/agentalias";
import { Button, Divider, Flex, Skeleton, Space } from "antd";
import {
    DeleteOutlined,
    SwapOutlined,
} from '@ant-design/icons';

interface AgentAliasCreateDrawerParams {
    onSwap: (record: AgentAlias) => void;
    onDelete: (record: AgentAlias) => void;
    data?: AgentAlias;
}

const AgentAliasViewDrawer: React.FC<AgentAliasCreateDrawerParams> = (params: AgentAliasCreateDrawerParams) => {
    const agentAliasController: AgentAliasController = new AgentAliasController();

    if (
        params.data === undefined
    ) return (<Skeleton />)

    return (
        <Space direction="vertical" style={{ padding: '0 24px' }}>
            <AgentAliasDescription layout="vertical" agentAlias={params.data} />
            <Divider style={{ color: '#ccc' }}>Actions</Divider>
            <Flex justify='center' align='center' gap="middle">
                <Button disabled={!agentAliasController.can(AgentAliasActions.SWAP, params.data)} type="text" icon={<SwapOutlined />} onClick={() => { params.onSwap(params.data) }}>Swap</Button>
                <Button disabled={!agentAliasController.can(AgentAliasActions.DELETE, params.data)} type="primary" danger icon={<DeleteOutlined />} onClick={() => { params.onDelete(params.data) }}>Delete</Button>
            </Flex>
        </Space>
    )
}

export default AgentAliasViewDrawer;