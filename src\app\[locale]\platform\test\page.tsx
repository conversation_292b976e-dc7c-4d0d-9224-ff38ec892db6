'use client'

import { BaseNode } from "@/components/flowbuilder/node/base-node";
import { FieldModel } from "@/components/flowbuilder/fields";
import { Behavior<PERSON>reeController } from "@/models/behaviortree";
import { <PERSON><PERSON>, Di<PERSON>r, <PERSON>lex, Result, Skeleton, Space, Steps, Typography } from "antd";
import React from "react";
import { ReactFlowProvider } from "@xyflow/react";
import CreateDnDContext, { DnDProvider } from "@/components/flowbuilder/dnd-context";
import FlowBuilder from "@/components/flowbuilder/flow-builder";
import { SetAgentNodeName } from "@/components/flowbuilder/node/setagent/set-agent-node";
import SetAgentNodeDescription from "@/components/flowbuilder/node/setagent/set-agent-description";
import SetAgentForm from "@/components/flowbuilder/node/setagent/set-agent-form";
import TaskNodeDescription from "@/components/flowbuilder/node/task-node/task-node-description";
import TaskNodeForm from "@/components/flowbuilder/node/task-node/task-node-form";
import { InitializeTaskNodeModelData } from "@/components/flowbuilder/node/task-node/task-node-model";
import { TaskNodeName } from "@/components/flowbuilder/node/task-node/task-node";
import { NodeTypes } from "@/components/flowbuilder/types";
import { RobotOutlined, BranchesOutlined, ForkOutlined, SelectOutlined, ReloadOutlined, SwitcherOutlined } from '@ant-design/icons';
import SetAgentNode from '@/components/flowbuilder/node/setagent/set-agent-node';
import TaskNode from "@/components/flowbuilder/node/task-node/task-node";

// Import new nodes
import { SequenceNodeName } from "@/components/flowbuilder/node/sequence/sequence-node";
import SequenceNodeDescription from "@/components/flowbuilder/node/sequence/sequence-node-description";
import SequenceNodeForm from "@/components/flowbuilder/node/sequence/sequence-node-form";
import { InitializeSequenceNodeModelData } from "@/components/flowbuilder/node/sequence/sequence-node-model";
import SequenceNode from "@/components/flowbuilder/node/sequence/sequence-node";

import { ParallelNodeName } from "@/components/flowbuilder/node/parallel/parallel-node";
import ParallelNodeDescription from "@/components/flowbuilder/node/parallel/parallel-node-description";
import ParallelNodeForm from "@/components/flowbuilder/node/parallel/parallel-node-form";
import { InitializeParallelNodeModelData } from "@/components/flowbuilder/node/parallel/parallel-node-model";
import ParallelNode from "@/components/flowbuilder/node/parallel/parallel-node";

import { SelectNodeName } from "@/components/flowbuilder/node/select/select-node";
import SelectNodeDescription from "@/components/flowbuilder/node/select/select-node-description";
import SelectNodeForm from "@/components/flowbuilder/node/select/select-node-form";
import { InitializeSelectNodeModelData } from "@/components/flowbuilder/node/select/select-node-model";
import SelectNode from "@/components/flowbuilder/node/select/select-node";

import { LoopNodeName } from "@/components/flowbuilder/node/loop/loop-node";
import LoopNodeDescription from "@/components/flowbuilder/node/loop/loop-node-description";
import LoopNodeForm from "@/components/flowbuilder/node/loop/loop-node-form";
import { InitializeLoopNodeModelData } from "@/components/flowbuilder/node/loop/loop-node-model";
import LoopNode from "@/components/flowbuilder/node/loop/loop-node";

import { SwitchNodeName } from "@/components/flowbuilder/node/switch/switch-node";
import SwitchNodeDescription from "@/components/flowbuilder/node/switch/switch-node-description";
import SwitchNodeForm from "@/components/flowbuilder/node/switch/switch-node-form";
import { InitializeSwitchNodeModelData } from "@/components/flowbuilder/node/switch/switch-node-model";
import SwitchNode from "@/components/flowbuilder/node/switch/switch-node";
import DataTable2Example from "@/components/data-table2-example";


const nodeComponents: NodeTypes = {
    [SetAgentNodeName]: {
        description: SetAgentNodeDescription,
        form: SetAgentForm,
        initializer: () => {return {}},
        node: SetAgentNode,
        icon: <RobotOutlined />,
        name: "Set Agent"
    },
    [TaskNodeName]: {
        description: TaskNodeDescription,
        form: TaskNodeForm,
        initializer: InitializeTaskNodeModelData,
        node: TaskNode,
        icon: <RobotOutlined />,
        name: "Task"
    },
    [SequenceNodeName]: {
        description: SequenceNodeDescription,
        form: SequenceNodeForm,
        initializer: InitializeSequenceNodeModelData,
        node: SequenceNode,
        icon: <BranchesOutlined />,
        name: "Sequence"
    },
    [ParallelNodeName]: {
        description: ParallelNodeDescription,
        form: ParallelNodeForm,
        initializer: InitializeParallelNodeModelData,
        node: ParallelNode,
        icon: <ForkOutlined />,
        name: "Parallel"
    },
    [SelectNodeName]: {
        description: SelectNodeDescription,
        form: SelectNodeForm,
        initializer: InitializeSelectNodeModelData,
        node: SelectNode,
        icon: <SelectOutlined />,
        name: "Select"
    },
    [LoopNodeName]: {
        description: LoopNodeDescription,
        form: LoopNodeForm,
        initializer: InitializeLoopNodeModelData,
        node: LoopNode,
        icon: <ReloadOutlined />,
        name: "Loop"
    },
    [SwitchNodeName]: {
        description: SwitchNodeDescription,
        form: SwitchNodeForm,
        initializer: InitializeSwitchNodeModelData,
        node: SwitchNode,
        icon: <SwitcherOutlined />,
        name: "Switch"
    }
};

export default () => {
    const behaviorTreeController = new BehaviorTreeController();
    const { data: behaviorTreeData, isLoading: isLoadingBehaviorTree } = behaviorTreeController.useGet(["2d4317a8-5fae-4b71-b1e8-5e51dbb45bbb"]);
    const DnDContext = React.useMemo(() => CreateDnDContext<any>(), []);
    const NodeTypes = React.useMemo(() => nodeComponents, []);

    const onSave = (nodes : BaseNode<any>[], edges : any[], fields : FieldModel[]) => {
        console.log('save');
        console.log(nodes);
        console.log(edges);
        console.log(fields);
        return true;
    }

    return (<DataTable2Example />);

    if (isLoadingBehaviorTree || !behaviorTreeData) {
        return <Skeleton />
    }

    return (
        <>
            <Flex vertical justify='stretch' style={{ height: '100%' }}>
                <Typography.Title level={2}>
                    Behavior Builder for 'asd'
                </Typography.Title>
                <Divider />
                <ReactFlowProvider>
                    <DnDProvider context={DnDContext}>
                        <FlowBuilder nodes={[]} connections={[]} save={onSave} nodeTypes={NodeTypes} context={DnDContext} />
                    </DnDProvider>
                </ReactFlowProvider>
            </Flex>
        </>
    );
}