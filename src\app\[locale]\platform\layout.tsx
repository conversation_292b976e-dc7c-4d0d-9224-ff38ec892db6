'use client'

import React, { Children } from 'react';
import { Layout, Menu, theme, Spin, Flex, Result } from 'antd';
import { DatabaseOutlined, FunctionOutlined, ContactsOutlined, ApartmentOutlined, CommentOutlined, UserOutlined, HomeOutlined, LogoutOutlined, KeyOutlined, SignatureOutlined, BranchesOutlined, EyeOutlined, CloudServerOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Routes as AppRoutes } from '@/constants';
import { useRouter, usePathname } from 'next/navigation';
import { useTransition } from "react";
import { LoginFunctions } from '@/functions/login';
import AuthenticationGate from '@/components/auth-gate';

type MenuItem = Required<MenuProps>['items'][number];

const { Header, Content, Sider } = Layout;

const siderStyle: React.CSSProperties = {
    overflow: 'auto',
    height: 'calc(100vh - 64px)',
    position: 'sticky',
    insetInlineStart: 0,
    top: 64,
    bottom: 0,
    scrollbarWidth: 'thin',
    scrollbarGutter: 'stable',
    backgroundColor: '#ffffff',
};

const headerStyle: React.CSSProperties = {
    overflow: 'auto',
    height: '64px',
    position: 'sticky',
    insetInlineStart: 0,
    top: 0,
    bottom: 0,
    scrollbarWidth: 'thin',
    scrollbarGutter: 'stable',
    display: 'flex',
    alignItems: 'center',
    backgroundColor: '#fff'
};

const items: MenuItem[] = [
    {
        key: AppRoutes.HOME,
        label: 'Home',
        icon: <HomeOutlined />
    },
    {
        key: 'agentgroup',
        label: 'AI Agents',
        type: 'group',
        children: [
    {
        key: AppRoutes.KNOWLEDGEBASE,
        label: 'Knowledge Bases',
        icon: <DatabaseOutlined />
    },
    {
        key: AppRoutes.AGENT,
        label: 'Agents',
        icon: <ContactsOutlined />
    },
    {
        key: 'agentaliases',
        label: 'Agent Aliases',
        icon: <SignatureOutlined />
    },
    {
        key: AppRoutes.AGENT_TOOLS,
        label: 'Agent Tools',
        icon: <FunctionOutlined />
    }]},
    {
        type: 'divider',
    },
    {
        key: 'flowgroup',
        label: 'Flows & Automation',
        type: 'group',
        children: [
    {
        key: '/platform/flows',
        label: 'Flows',
        icon: <BranchesOutlined />
    },
    {
        key: '/platform/traces',
        label: 'Traces',
        icon: <EyeOutlined />
    },
    {
        key: '/platform/mcpservers',
        label: 'MCP Servers',
        icon: <CloudServerOutlined />
    }]},
    {
        type: 'divider',
    },
    {
        key: 'apigroup',
        label: 'API Control',
        type: 'group',
        children: [
    {
        key: AppRoutes.API_KEYS,
        label: 'API Keys',
        icon: <KeyOutlined />
    },
    {
        key: 'sessions',
        label: 'Sessions',
        icon: <CommentOutlined />
    }]},
    {
        type: 'divider',
    },
    {
        key: 'managementgroup',
        label: 'Management',
        type: 'group',
        children: [
    {
        key: 'users',
        label: 'Users',
        icon: <UserOutlined />
    },
    {
        type: 'divider',
    },
    {
        key: 'logout',
        label: 'Log Out',
        icon: <LogoutOutlined />
    }]}
];

const boxStyle: React.CSSProperties = {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 1)',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 9999,
    animation: 'fadeIn 1.5s',
};


export default function PlatformLayout({
    children
}: Readonly<{
    children: React.ReactNode;
}>) {
    const router = useRouter();

    const [collapsed, setCollapsed] = React.useState(false);
    const {
        token: { colorBgContainer, borderRadiusLG },
    } = theme.useToken();
    const currentPath = usePathname();

    const [selectedKey, setSelectedKey] = React.useState([currentPath]);
    const [isPending, startTransition] = useTransition();

    const onMenuSelectionChange = (data: any) => {
        setSelectedKey([data.key]);
        if(data.key == "logout"){
            LoginFunctions.Logout();
        }else{
            startTransition(() => {
                router.push(data.key);
            })
        }

    }

    return (
        <>
            <AuthenticationGate requireLogIn={true}>
            <Layout>
                <Header style={headerStyle}>
                    <img alt="coral agents" src="/images/logo.png" height={52} />
                </Header>
                <Layout hasSider>
                    <Sider style={siderStyle} collapsible collapsed={collapsed} onCollapse={(value: boolean) => setCollapsed(value)}>
                        <div className="demo-logo-vertical" />

                        <Menu
                            defaultSelectedKeys={selectedKey}
                            mode="inline"
                            items={items}
                            onSelect={onMenuSelectionChange}
                        />

                    </Sider>
                    <Layout style={{ padding: '0 24px 24px' }}>


                        <Content
                            style={{
                                padding: 24,
                                margin: 0,
                                marginTop: 24,
                                minHeight: 280,
                                background: colorBgContainer,
                                borderRadius: borderRadiusLG,
                                overflow: 'initial',
                            }}
                        >

                            <div className='container' style={{ position: 'relative', height: '100%', width: '100%' }}>
                                {(isPending) ? <Flex style={boxStyle} justify='center' align='center'><Spin /></Flex> : <></>}
                                {children}
                            </div>
                        </Content>
                    </Layout>
                </Layout>
            </Layout>
            </AuthenticationGate>
        </>
    );
}