import { AgentKnowledgebaseStatus } from "@/models/agentknowledgebase"
import { Tag } from "antd"
import {
    CheckCircleOutlined,
    SyncOutlined,
    CloseCircleOutlined
} from '@ant-design/icons';

export function GetAgentKnowledgebaseStatusTag(status: AgentKnowledgebaseStatus) {
    switch (status) {
        case AgentKnowledgebaseStatus.ASSIGNING:
            return (<Tag icon={<SyncOutlined spin />} color="purple">assigning</Tag>)
        case AgentKnowledgebaseStatus.READY:
            return (<Tag icon={<CheckCircleOutlined />} color="success">ready</Tag>)
        case AgentKnowledgebaseStatus.UNASSIGNING:
            return (<Tag icon={<SyncOutlined spin />} color="red">unassigning</Tag>)
        case AgentKnowledgebaseStatus.UNASSIGNED:
            return (<Tag icon={<SyncOutlined spin />} color="red">unassigning</Tag>)
        case AgentKnowledgebaseStatus.DELETED:
            return (<Tag icon={<CloseCircleOutlined />} color="red">deleted</Tag>)
        default:
            return (<Tag color="default">unknown</Tag>)
    }
}