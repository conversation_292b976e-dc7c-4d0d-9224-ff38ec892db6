'use client'

import { BaseNode } from "@/components/flowbuilder/node/base-node";
import { FieldModel } from "@/components/flowbuilder/fields";
import { Behavior<PERSON>reeController } from "@/models/behaviortree";
import { AgentController } from "@/models/agent";
import { <PERSON><PERSON>, <PERSON><PERSON>r, <PERSON>lex, Result, Skeleton, Space, Typography, message } from "antd";
import React, { use } from "react";
import { ReactFlowProvider } from "@xyflow/react";
import CreateDnDContext, { DnDProvider } from "@/components/flowbuilder/dnd-context";
import FlowBuilder from "@/components/flowbuilder/flow-builder";
import { SetAgentNodeName } from "@/components/flowbuilder/node/setagent/set-agent-node";
import SetAgentNodeDescription from "@/components/flowbuilder/node/setagent/set-agent-description";
import SetAgentForm from "@/components/flowbuilder/node/setagent/set-agent-form";
import SetAgentNode from "@/components/flowbuilder/node/setagent/set-agent-node";
import { TaskNodeName } from "@/components/flowbuilder/node/task-node/task-node";
import TaskNodeDescription from "@/components/flowbuilder/node/task-node/task-node-description";
import TaskNodeForm from "@/components/flowbuilder/node/task-node/task-node-form";
import { InitializeTaskNodeModelData } from "@/components/flowbuilder/node/task-node/task-node-model";
import TaskNode from "@/components/flowbuilder/node/task-node/task-node";
import { SequenceNodeName } from "@/components/flowbuilder/node/sequence/sequence-node";
import SequenceNodeDescription from "@/components/flowbuilder/node/sequence/sequence-node-description";
import SequenceNodeForm from "@/components/flowbuilder/node/sequence/sequence-node-form";
import { InitializeSequenceNodeModelData } from "@/components/flowbuilder/node/sequence/sequence-node-model";
import SequenceNode from "@/components/flowbuilder/node/sequence/sequence-node";
import { NodeTypes } from "@/components/flowbuilder/types";
import { useRouter } from "next/navigation";
import {
    RobotOutlined,
    BranchesOutlined,
    ArrowLeftOutlined,
} from '@ant-design/icons';

interface AgentBehaviorPageProps {
    params: Promise<{
        agentId: string;
    }>;
}

const DnDContext = CreateDnDContext<string>();

const nodeComponents: NodeTypes = {
    [SetAgentNodeName]: {
        description: SetAgentNodeDescription,
        form: SetAgentForm,
        initializer: () => {return {}},
        node: SetAgentNode,
        icon: <RobotOutlined />,
        name: "Set Agent"
    },
    [TaskNodeName]: {
        description: TaskNodeDescription,
        form: TaskNodeForm,
        initializer: InitializeTaskNodeModelData,
        node: TaskNode,
        icon: <RobotOutlined />,
        name: "Task"
    },
    [SequenceNodeName]: {
        description: SequenceNodeDescription,
        form: SequenceNodeForm,
        initializer: InitializeSequenceNodeModelData,
        node: SequenceNode,
        icon: <BranchesOutlined />,
        name: "Sequence"
    },
};

export default function AgentBehaviorPage({ params }: AgentBehaviorPageProps) {
    const { agentId } = use(params);
    const router = useRouter();
    const agentController = new AgentController();
    const behaviorTreeController = new BehaviorTreeController();

    const { data: agentData, isLoading: isLoadingAgent } = agentController.useGet([agentId]);

    // Try to get existing behavior tree for this agent
    const { data: behaviorTreeData, isLoading: isLoadingBehaviorTree } = behaviorTreeController.useListByAgent(agentId);

    const { trigger: createBehaviorTree, isMutating: isCreating } = behaviorTreeController.useCreate();
    const { trigger: updateBehaviorTree, isMutating: isUpdating } = behaviorTreeController.useUpdate();

    const onSave = async (nodes: BaseNode<any>[], edges: any[], fields: FieldModel[]) => {
        try {
            const treeDefinition = JSON.stringify({ nodes, edges, fields });

            const existingBehaviorTree = behaviorTreeData?.entries?.[0];

            if (existingBehaviorTree) {
                // Update existing behavior tree
                await updateBehaviorTree({
                    body: {
                        id: existingBehaviorTree.id,
                        name: `${agent.name} Behavior`,
                        description: `Behavior tree for agent ${agent.name}`,
                        treeDefinition,
                        agentId: agentId
                    }
                });
            } else {
                // Create new behavior tree
                await createBehaviorTree({
                    body: {
                        name: `${agent.name} Behavior`,
                        description: `Behavior tree for agent ${agent.name}`,
                        agentId: agentId,
                        treeDefinition
                    }
                });
            }

            message.success('Agent behavior saved successfully!');
            return true;
        } catch (error) {
            message.error('Failed to save agent behavior');
            console.error('Error saving behavior tree:', error);
            return false;
        }
    };

    if (isLoadingAgent || !agentData || agentData.length === 0) {
        return <Skeleton />;
    }

    const agent = agentData[0];

    // Parse existing behavior tree data if available
    let initialNodes: BaseNode<any>[] = [];
    let initialConnections: any[] = [];
    let initialFields: FieldModel[] = [];

    if (behaviorTreeData?.entries?.[0]?.treeDefinition) {
        try {
            const parsed = JSON.parse(behaviorTreeData.entries[0].treeDefinition);
            initialNodes = parsed.nodes || [];
            initialConnections = parsed.edges || [];
            initialFields = parsed.fields || [];
        } catch (error) {
            console.error('Error parsing behavior tree definition:', error);
        }
    }

    if (!agent) {
        return (
            <Result
                status="404"
                title="Agent Not Found"
                subTitle="The agent you're looking for doesn't exist."
                extra={
                    <Button type="primary" onClick={() => router.push('/platform/agent')}>
                        Back to Agents
                    </Button>
                }
            />
        );
    }

    return (
        <>
            <Flex vertical justify='stretch' style={{ height: '100%' }}>
                <Flex align="center" gap="middle" style={{ marginBottom: 16 }}>
                    <Button 
                        type="text" 
                        icon={<ArrowLeftOutlined />} 
                        onClick={() => router.push('/platform/agent')}
                    >
                        Back to Agents
                    </Button>
                    <Typography.Title level={2} style={{ margin: 0 }}>
                        Edit Behavior for "{agent.name}"
                    </Typography.Title>
                </Flex>
                <Divider />
                <ReactFlowProvider>
                    <DnDProvider context={DnDContext}>
                        <FlowBuilder
                            nodes={initialNodes}
                            connections={initialConnections}
                            save={onSave}
                            nodeTypes={nodeComponents}
                            context={DnDContext}
                            initialFields={initialFields}
                        />
                    </DnDProvider>
                </ReactFlowProvider>
            </Flex>
        </>
    );
}
