import { <PERSON><PERSON><PERSON><PERSON>, FlowAliasController } from "@/models/flow/flow-alias";
import { Button, Dropdown, Space } from "antd";
import type { TableColumnsType } from 'antd';
import React from "react";
import {
    EllipsisOutlined,
    EditOutlined,
    DeleteOutlined,
    SwapOutlined,
} from '@ant-design/icons';
import DataTable from "@/components/data-table";
import { useTimeAgo } from "next-timeago";

interface FlowAliasListProps {
    mutateObjects?: FlowAlias[];
    onEdit?: (record: FlowAlias) => void;
    onDelete?: (record: FlowAlias) => void;
    onSwap?: (record: FlowAlias) => void;
}

const FlowAliasList: React.FC<FlowAliasListProps> = (params) => {
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState<boolean>(false);

    const contextMenuProps = (record: Flow<PERSON>lias) => ({
        items: [
            {
                key: 'edit',
                label: 'Edit',
                icon: <EditOutlined />,
                onClick: () => {
                    if (params.onEdit) {
                        params.onEdit(record);
                    }
                }
            },
            {
                key: 'swap',
                label: 'Swap Tag',
                icon: <SwapOutlined />,
                onClick: () => {
                    if (params.onSwap) {
                        params.onSwap(record);
                    }
                }
            },
            {
                type: 'divider'
            },
            {
                key: 'delete',
                label: 'Delete',
                icon: <DeleteOutlined />,
                danger: true,
                onClick: () => {
                    if (params.onDelete) {
                        params.onDelete(record);
                    }
                }
            }
        ]
    });

    const columns: TableColumnsType<FlowAlias> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Alias', 
            dataIndex: 'alias'
        },
        {
            title: 'Description', 
            dataIndex: 'description',
            ellipsis: true
        },
        {
            title: 'Flow Name', 
            dataIndex: 'flowName'
        },
        {
            title: 'Tag Name', 
            dataIndex: 'tagName'
        },
        {
            title: 'Created At', 
            dataIndex: 'createdAt', 
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        },
        {
            title: 'Last Modified', 
            dataIndex: 'lastChangeTimestamp', 
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<FlowAlias, FlowAliasController>
            controller={new FlowAliasController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: FlowAlias) => false}
            mutateItems={params.mutateObjects}
            rowKey="id"
        />
    )
}

export default FlowAliasList;
