import { Fetcher2 } from "@/functions/fetcher2";
import { KeyedMutator, SWRConfiguration } from "swr";

export interface ListRequest{
    count: number;
    next?: string;
}

export interface ListResponse<T extends IBasemodel>{
    entries: T[];
    next?: string;
    total: number;
}

export interface DeleteMultiRequest{
    ids : string[];
}

export interface DeleteMultiResponse{
    deletedIds : string[];
}

export interface IBasemodel extends Object {
    lastChangeTimestamp : number;
    createdAt : number;
}

export interface IBaseModelSearchController<T extends IBasemodel>{
    getId : (item : T) => string;
    
    useGet : (ids: string[], options?: SWRConfiguration) => {
        data: T[] | undefined;
        error: boolean;
        isValidating: boolean;
        isLoading: boolean;
        mutate: KeyedMutator<any>;
    }
    
    useSearch : (searchQuery? : string, limit?: number) => {
        data: ListResponse<T> | undefined;
        error: boolean;
        isValidating: boolean;
        isLoading: boolean;
        mutate: KeyedMutator<any>;
    };
}

export interface IBaseModelSearchController2<T extends IBasemodel>{
    useSearch : (searchQuery? : string, limit?: number) => {
        data: ListResponse<T>;
        error: boolean;
        trigger: (params?: Fetcher2.SWRMutationParams) => Promise<any>;
        isMutating: boolean;
    };
}

export interface IBasemodelController<T extends IBasemodel, TActions>{
    //getIdFieldName : () => string;
    getId : (item : T) => string;

    useList : (request : ListRequest) => {
        data: ListResponse<T> | undefined;
        error: any;
        isLoading: boolean;
        mutate: KeyedMutator<ListResponse<T>>;
    }

    useGet : (ids: string[], options?: SWRConfiguration) => {
        data: T[] | undefined;
        error: boolean;
        isValidating: boolean;
        isLoading: boolean;
        mutate: KeyedMutator<any>;
    }

    useDelete : (id : string) =>  {
        data: T;
        error: boolean;
        trigger: (params?: Fetcher2.SWRMutationParams) => Promise<any>;
        isMutating: boolean;
    }

    can : (action : TActions, onItem : T) => boolean;
}