'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ontroller } from "@/models/apikey";
import <PERSON><PERSON><PERSON><PERSON><PERSON>ist from "./list/apikey-list";
import { <PERSON><PERSON>, Divider, Drawer, Space, Typography } from "antd";
import ConfirmDeleteModal from "@/components/confirm-delete-modal";
import React from "react";
import {
    PlusOutlined,
} from '@ant-design/icons';
import ApiKeyCreateDrawer from "./drawers/create-drawer";
import ApiKeyViewDrawer from "./drawers/view-drawer";

type ApiKeysComponentProps = any;

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions {
    apiKey?: ApiKey;
    open: boolean;
}

const ApiKeysComponent: React.FC<ApiKeysComponentProps> = (props: ApiKeysComponentProps) => {
    const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
    const [mutateObject, setMutateObject] = React.useState<ApiKey[]>([]);
    const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({ open: false });

    const onCloseDrawer = () => {
        setDrawerOptions(emptyDrawer);
    };
    
    const onCreate = () => {
        setDrawerOptions(
            {
                title: "Edit Agent",
                isOpen: true,
                component: <ApiKeyCreateDrawer
                    isOpen={true}
                    onSuccess={(data: ApiKey) => { setDrawerOptions(emptyDrawer); setMutateObject([data]) }}
                />
            }
        )
    }

    const onView = (record: ApiKey) => {
        setDrawerOptions(
            {
                title: "View Key",
                isOpen: true,
                component: <ApiKeyViewDrawer
                    data={record}
                    onDelete={(record) => onDelete(record)}
                    onPause={(record, pause) => onPause(record, pause)}
                />
            }
        )
    }

    const onPause = (record: ApiKey, pause : boolean) => {
    }

    const onDelete = (record: ApiKey) => {
        setDeleteModalOptions({ open: true, apiKey: record })
    }

    const onDeleteConfirm = (record: ApiKey) => {
        setDeleteModalOptions({ open: false })
        setMutateObject([record])
    }

    const onDeleteCancel = (record: ApiKey) => {
        setDeleteModalOptions({ open: false })
    }

    return (
        <>
            <ConfirmDeleteModal<ApiKey>
                controller={new ApiKeyController()}
                open={deleteModalOptions.open}
                objectToDelete={deleteModalOptions.apiKey}
                onDelete={onDeleteConfirm}
                onCancel={onDeleteCancel}
            />
            <Typography.Title level={2}>
                API Keys
            </Typography.Title>
            <Button size='large' type='primary' onClick={() => onCreate()}>
                <PlusOutlined />Create
            </Button>
            <Divider />
            <ApiKeyList
                onClick={(record) => onView(record)}
                onDelete={(record) => onDelete(record)}
                onPause={(record, pause) => onPause(record, pause)}
                mutateObjects={mutateObject}
            />
            <Drawer
                title={drawerOptions.title}
                placement="right"
                size="large"
                onClose={onCloseDrawer}
                open={drawerOptions.isOpen}
                extra={
                    <Space>
                        <Button onClick={onCloseDrawer}>Cancel</Button>
                    </Space>
                }
            >
                {drawerOptions.component}
            </Drawer>
        </>
    )
}


export default ApiKeysComponent;