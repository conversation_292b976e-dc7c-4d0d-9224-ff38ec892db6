import { Button, Flex, Form } from "antd";
import TextArea from "antd/es/input/TextArea";
import React from "react";

interface AssignForm {
    description: string;
}

export interface AgentKnowledgebaseAssignFormParams {
    onSubmit: (formData: AssignForm) => void;
    onCancel: () => void;
    disableSubmit: boolean;
    isLoading: boolean;
}


const AgentKnowledgebaseAssignForm: React.FC<AgentKnowledgebaseAssignFormParams> = (params: AgentKnowledgebaseAssignFormParams) => {
    const [assignForm] = Form.useForm<AssignForm>();
    const [formIsInvalidated, setFormIsInvalidated] = React.useState(true);

    const disableSubmit = (
        params.disableSubmit ||
        !assignForm.isFieldsTouched() ||
        formIsInvalidated ||
        params.isLoading
    );

    return (
        <Form form={assignForm}>
            <Form.Item
                label="Description"
                rules={[{ required: true, message: 'Description is required.' }, { min: 10, message: 'Please add a description with at least 10 characters.' }]}
                name="description"
            >
                <TextArea
                    showCount
                    maxLength={128}
                    placeholder="Description for this association"
                    style={{ resize: 'none' }}
                    onChange={async () => {
                        const values = await assignForm.validateFields().then(
                            (value) => setFormIsInvalidated(false)
                        ).catch(
                            (reason) => setFormIsInvalidated(reason.errorFields.length > 0)
                        )
                    }}
                />
            </Form.Item>
            <Flex justify="flex-end" gap={'middle'}>
                <Form.Item shouldUpdate>
                    <Button type="primary" disabled={disableSubmit}
                        onClick={() => {
                            assignForm.validateFields().then((value) => {
                                if (disableSubmit) return;
                                params.onSubmit(assignForm.getFieldsValue());
                            })
                        }}
                    > Assign Knowledgebase to this Agent</Button>

                </Form.Item>
                <Button type="default" disabled={params.isLoading} onClick={() => { assignForm.resetFields(); params.onCancel(); }}>Cancel</Button>
            </Flex>
        </Form>
    )
}

export default AgentKnowledgebaseAssignForm;