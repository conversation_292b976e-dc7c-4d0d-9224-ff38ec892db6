'use client';

import { Button, Checkbox, Form, Input, Select, Segmented, Space } from 'antd';
import React from 'react';
import { FieldModel, FieldModelType } from './field-model';
import SubmitButton from '@/components/submit-button';



interface VariablesFormProps {
    onSave: (values: FieldModel) => void;
    onShow?: () => void;
    onClose?: () => void;
    fields : FieldModel[];
    formType: FieldModelType
}

const makeId = (formType : FieldModelType, name : string) => formType + "/" + name


const VariablesForm: React.FC<VariablesFormProps> = (props) => {
    const [form] = Form.useForm<FieldModel>();
    const [isShowing, setIsShowing] = React.useState(false);

    const typeOptions = [
        { value: 'STRING', label: 'STRING' },
        { value: 'INTEGER', label: 'INTEGER' },
        { value: 'FLOAT', label: 'FLOAT' },
        { value: 'BOOLEAN', label: 'BOOLEAN' },
        { value: 'JSON', label: 'JSON' }
    ];

    if (!isShowing) return <Button onClick={() => { setIsShowing(true); form.resetFields(); if (props.onShow) props.onShow() }}>Add a new {props.formType == 'input' ? 'Input' : 'Variable'}</Button>
    const validateName = async (rule: any, value: any) => {
        const id = makeId(props.formType, value);
        if (props.fields.filter(f => f.id == id).length > 0) {
            throw new Error('This name is already in use!');
        }
    }

    return (
        <Form
            form={form}
            layout="vertical"
            onFinish={(values) => {
                const submitValues : FieldModel= {
                    dataType: values.dataType,
                    defaultValue: values.defaultValue ?? null,
                    name: values.name,
                    required: values.required ?? false,
                    id: props.formType + "/" + values.name,
                    type: props.formType
                }
                setIsShowing(false);
                props.onSave(submitValues);
                props.onClose && props.onClose();
            }}
        >
            <Form.Item
                label={props.formType === 'variable' ? "Variable Name" : "Input Name"}
                name="name"
                rules={[
                    { required: true, message: 'Please input a name!' },
                    { validator: validateName }
                ]}
            >
                <Input />
            </Form.Item>

            <Form.Item
                label={props.formType === 'variable' ? "Variable Type" : "Input Type"}
                name="dataType"
                rules={[{ required: true, message: 'Please select a type!' }]}
            >
                <Select options={typeOptions} />
            </Form.Item>

            <Form.Item
                label={props.formType === 'variable' ? "Initial Value" : "Default Value"}
                name="defaultValue"
            >
                <Input />
            </Form.Item>

            {props.formType === 'input' && (
                <Form.Item
                    name="required"
                    valuePropName="checked"
                >
                    <Checkbox>Required</Checkbox>
                </Form.Item>
            )}

            <Space>
                <SubmitButton form={form}>
                    Save
                </SubmitButton>
                <Button onClick={() => { setIsShowing(false); if (props.onClose) props.onClose() }}>
                    Cancel
                </Button>
            </Space>
        </Form>
    );
};

export default VariablesForm;
