import { IBasemodel, IBasemodelController } from "@/models/base-model";
import { Popconfirm } from "antd";
import React from "react";
import { PropsWithChildren } from "react";

interface ConfirmDeleteProps<T extends IBasemodel> {
    objectToDelete: T;
    onDeleteStart: (obj : T) => void;
    onDelete: (obj: T) => void;
    controller : IBasemodelController<T>;
    title? : string;
    description? : string;
}

function ConfirmDeletePopout<T extends IBasemodel>(props: PropsWithChildren<ConfirmDeleteProps<T>>) {
    const { data , error, trigger, isMutating} = props.controller.useDelete(props.controller.getId(props.objectToDelete))

    React.useEffect(()=>{
        if(data) props.onDelete(data)
    },[data])

    return (
        <Popconfirm
            title={(props.title) ? props.title : "Are you sure to delete?"}
            description={(props.description) ? props.description : "This action is irreversible."}
            onConfirm={(message) => {
                props.onDeleteStart(props.objectToDelete);
                trigger();
            }}
            okText="Yes"
            cancelText="No"
        >
            {props.children}
        </Popconfirm>
    );
}

export default ConfirmDeletePopout;