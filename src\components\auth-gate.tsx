'use client'

import { LoginFunctions } from '@/functions/login'
import { Children, useEffect } from 'react';
import React from 'react';
import { Routes } from '@/constants'
import { useRouter } from 'next/navigation';
import { Result } from 'antd';


type Props = {
    children?: React.ReactNode
    requireLogIn?: boolean;
};


const AuthenticationGate: React.FC<Props> = ({ children, requireLogIn }) => {
    const router = useRouter();
    const [isLoggedIn, setIsLoggedIn] = React.useState(null);

    useEffect(() => {
        setTimeout(() => {setIsLoggedIn(LoginFunctions.IsLoggedIn())}, 0);
    }, []);

    if(isLoggedIn == null){
        return <></>;
    }

    if (requireLogIn ?? true) {
        if (!isLoggedIn) {
            setTimeout(() => {
                router.push(Routes.LOGIN);
            }, 3000);
            return (<Result
                status="error"
                title="You are not logged in."
                subTitle="Please login again or request access to the platform if you clicked this link."
                extra={[
                    <div key="redirecting">Redirecting you in 3 seconds...</div>
                ]}
            >
            </Result>)
        } else {
            LoginFunctions.RefreshLoginJwt();
            return (<>{children}</>)
        }
    } else {
        if (isLoggedIn) {
            setTimeout(() => {
                router.push(Routes.HOME);
            }, 3000);
            return (<Result
                status="success"
                title="You are already logged in."
                subTitle="We will redirect you to the platform."
                extra={[
                    <div key="redirecting">Redirecting you in 3 seconds...</div>
                ]}
            >
            </Result>)
        } else {
            return (<>{children}</>)
        }

    }
}


export default AuthenticationGate;