'use client'

import { Flow, FlowController } from "@/models/flow";
import FlowList from "./list/flow-list";
import { <PERSON><PERSON>, Divider, Drawer, Space, Typography } from "antd";
import ConfirmDeleteModal from "@/components/confirm-delete-modal";
import React from "react";
import {
    PlusOutlined,
} from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import FlowPutDrawer from "./drawers/put-drawer";
import { useRouter } from "next/navigation";

type DrawerState = { title: string; isOpen: boolean; component: React.JSX.Element; };
const emptyDrawer: DrawerState = { title: "", isOpen: false, component: <></> }

interface DeleteModalOptions {
    flow?: Flow;
    open: boolean;
}

const FlowsComponent: React.FC = () => {
    const [drawerOptions, setDrawerOptions] = React.useState(emptyDrawer);
    const [mutateObject, setMutateObject] = React.useState<Flow[]>([]);
    const [deleteModalOptions, setDeleteModalOptions] = React.useState<DeleteModalOptions>({ open: false });
    const router = useRouter();

    const onCloseDrawer = () => {
        setDrawerOptions(emptyDrawer);
    };

    const onCreate = () => {
        setDrawerOptions({ title: "Create Flow", isOpen: true, component: <FlowPutDrawer isOpen={true} onSuccess={onSuccess} mode='create' /> })
    }

    const onView = (record: Flow) => {
        // You can implement a view drawer similar to BehaviorTree if needed
    }

    const onFlowBuilder = (record: Flow) => {
        router.push(`/platform/flows/${record.id}/flowbuilder`);
    }

    const onEdit = (record: Flow) => {
        setDrawerOptions({ title: "Edit Flow", isOpen: true, component: <FlowPutDrawer isOpen={true} onSuccess={onSuccess} mode='edit' flow={record} /> })
    }

    const onSuccess = (record: Flow) => {
        setMutateObject([record])
    }

    const onDelete = (record: Flow) => {
        setDeleteModalOptions({ open: true, flow: record })
    }

    const onDeleteConfirm = (record: Flow) => {
        setDeleteModalOptions({ open: false });
        setMutateObject([record]);
    }

    const onDeleteCancel = () => {
        setDeleteModalOptions({ open: false });
    }

    return (
        <>
            <ConfirmDeleteModal<Flow>
                controller={new FlowController()}
                open={deleteModalOptions.open}
                objectToDelete={deleteModalOptions.flow}
                onDelete={onDeleteConfirm}
                onCancel={onDeleteCancel}
            />
            <Typography.Title level={2}>
                Flows
            </Typography.Title>
            <Button size='large' type='primary' onClick={() => onCreate()}>
                <PlusOutlined />Create
            </Button>
            <Divider />
            <FlowList
                onClick={(record) => onView(record)}
                onDelete={(record) => onDelete(record)}
                onEdit={(record) => onEdit(record)}
                onFlowBuilder={(record) => onFlowBuilder(record)}
                mutateObjects={mutateObject}
            />
            <Drawer
                title={drawerOptions.title}
                placement="right"
                size="large"
                onClose={onCloseDrawer}
                open={drawerOptions.isOpen}
                extra={
                    <Space>
                        <Button onClick={onCloseDrawer}>Cancel</Button>
                    </Space>
                }
            >
                {drawerOptions.component}
            </Drawer>
        </>
    )
}

export default FlowsComponent;