import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, MCPServerDeployRequest } from "@/models/mcpserver";
import { Button, Space, Typography, Alert, Descriptions, message } from "antd";
import React from "react";
import {
    PlayCircleOutlined,
    WarningOutlined,
} from '@ant-design/icons';

interface MCPServerDeployDrawerProps {
    mcpServer: MCPServer;
    isOpen: boolean;
    onSuccess: (mcpServer: MCPServer) => void;
    onClose: () => void;
}

const MCPServerDeployDrawer: React.FC<MCPServerDeployDrawerProps> = ({ 
    mcpServer, 
    isOpen, 
    onSuccess, 
    onClose 
}) => {
    const mcpServerController = new MCPServerController();
    const { trigger: deployTrigger, isMutating: isDeploying } = mcpServerController.useDeploy(mcpServer.id);

    const handleDeploy = async () => {
        try {
            const request: MCPServerDeployRequest = {
                id: mcpServer.id
            };
            
            const result = await deployTrigger(request);
            
            if (result) {
                message.success('MCP Server deployment started successfully');
                onSuccess(result);
            }
        } catch (error) {
            message.error('Failed to deploy MCP server');
        }
    };

    return (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Typography.Title level={4}>Deploy MCP Server</Typography.Title>
            
            <Alert
                message="Deployment Confirmation"
                description="You are about to deploy this MCP server. This will make it available for external connections."
                type="info"
                showIcon
                icon={<WarningOutlined />}
            />

            <Descriptions bordered column={1} size="small">
                <Descriptions.Item label="Server Name">
                    {mcpServer.name}
                </Descriptions.Item>
                <Descriptions.Item label="Description">
                    {mcpServer.description}
                </Descriptions.Item>
                <Descriptions.Item label="Flow">
                    {mcpServer.flowName || 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="Flow Alias">
                    {mcpServer.flowAlias}
                </Descriptions.Item>
            </Descriptions>

            <Alert
                message="What happens during deployment?"
                description={
                    <ul style={{ margin: 0, paddingLeft: 20 }}>
                        <li>The server will be provisioned with the selected flow</li>
                        <li>An endpoint will be created for external access</li>
                        <li>The server status will change to "Deploying" then "Running"</li>
                        <li>You will be able to connect to the server using the provided endpoint</li>
                    </ul>
                }
                type="warning"
                showIcon
            />

            <Space>
                <Button 
                    type="primary" 
                    icon={<PlayCircleOutlined />}
                    loading={isDeploying}
                    onClick={handleDeploy}
                >
                    {isDeploying ? 'Deploying...' : 'Deploy Server'}
                </Button>
                <Button onClick={onClose} disabled={isDeploying}>
                    Cancel
                </Button>
            </Space>
        </Space>
    );
};

export default MCPServerDeployDrawer;
