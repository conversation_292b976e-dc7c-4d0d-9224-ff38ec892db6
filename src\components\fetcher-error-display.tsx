import { Fetcher2 } from "@/functions/fetcher2";
import { Alert, Result } from "antd";

interface FetcherErrorDisplayProps {
    type: 'page' | 'alert';
    error: Fetcher2.FetcherError;
}


const FetcherErrorDisplay: React.FC<FetcherErrorDisplayProps> = (props) => {
    if (!props.error) return <></>

    if (props.type === 'page')
        return <Result
            status="error"
            title={props.error.name}
            subTitle={props.error.message}
        />;

    if (props.type === "alert")
        return <Alert
            message={props.error.name}
            description={props.error.message}
            type="error"
            banner={true}
            showIcon
            closable
        />

    return <></>
}

export default FetcherErrorDisplay;